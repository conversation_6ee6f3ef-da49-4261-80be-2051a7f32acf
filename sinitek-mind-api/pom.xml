<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sinitek.sinicube</groupId>
        <artifactId>sini-mind-backend</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>sinitek-mind-api</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-vector-store</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-sinicube-utils</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <!-- JDK8 profile: 排除不支持 JDK 8 的依赖和源文件 -->
        <profile>
            <id>jdk8</id>
            <dependencies>
                <dependency>
                    <groupId>com.sinitek.sinicube</groupId>
                    <artifactId>sinitek-sinicube-utils</artifactId>
                    <scope>provided</scope>
                    <optional>true</optional>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <!-- 配置编译器排除使用 Spring AI 的源文件 -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <configuration>
                            <source>1.8</source>
                            <target>1.8</target>
                            <encoding>UTF-8</encoding>
                            <excludes>
                                <!-- 排除使用 Spring AI 的源文件 -->
                                <exclude>**/vector/**/*.java</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>