<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sinitek.sinicube</groupId>
        <artifactId>sini-mind-backend</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>sinitek-mind-app</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-mind-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-rag</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-advisors-vector-store</artifactId>
        </dependency>

        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-community-xinference</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-mind-plugins-vector-store-milvus</artifactId>
        </dependency>

        <!-- 文件解析分块依赖 Begin -->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-pdf-document-reader</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-jsoup-document-reader</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-markdown-document-reader</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-tika-document-reader</artifactId>
        </dependency>

        <!-- 文件编码检测依赖 -->
        <dependency>
            <groupId>com.github.albfernandez</groupId>
            <artifactId>juniversalchardet</artifactId>
            <version>2.5.0</version>
        </dependency>
        <!-- 文件解析分块依赖 End -->

        <!-- mongodb依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <!-- postgresql-->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-openai</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-ollama</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-sirmapp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>io.swagger.parser.v3</groupId>
            <artifactId>swagger-parser</artifactId>
            <version>2.1.12</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-mcp-client-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <descriptors>
                        <descriptor>src/main/resources/assembly/default.xml</descriptor>
                    </descriptors>
                </configuration>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!-- JDK8 profile: 不使用 maven-assembly-plugin 和 spring-boot-maven-plugin -->
        <profile>
            <id>jdk8</id>
            <dependencies>
                <!-- 覆盖 sinitek-mind-api 依赖，添加 jdk8 classifier -->
                <dependency>
                    <groupId>com.sinitek.sinicube</groupId>
                    <artifactId>sinitek-mind-api</artifactId>
                    <version>${project.version}</version>
                    <classifier>jdk8</classifier>
                </dependency>

                <!-- 排除所有 Spring AI 相关依赖 -->
                <dependency>
                    <groupId>org.springframework.ai</groupId>
                    <artifactId>spring-ai-rag</artifactId>
                    <scope>provided</scope>
                    <optional>true</optional>
                </dependency>

                <dependency>
                    <groupId>org.springframework.ai</groupId>
                    <artifactId>spring-ai-advisors-vector-store</artifactId>
                    <scope>provided</scope>
                    <optional>true</optional>
                </dependency>

                <dependency>
                    <groupId>org.springframework.ai</groupId>
                    <artifactId>spring-ai-pdf-document-reader</artifactId>
                    <scope>provided</scope>
                    <optional>true</optional>
                </dependency>

                <dependency>
                    <groupId>org.springframework.ai</groupId>
                    <artifactId>spring-ai-jsoup-document-reader</artifactId>
                    <scope>provided</scope>
                    <optional>true</optional>
                </dependency>

                <dependency>
                    <groupId>org.springframework.ai</groupId>
                    <artifactId>spring-ai-markdown-document-reader</artifactId>
                    <scope>provided</scope>
                    <optional>true</optional>
                </dependency>

                <dependency>
                    <groupId>org.springframework.ai</groupId>
                    <artifactId>spring-ai-tika-document-reader</artifactId>
                    <scope>provided</scope>
                    <optional>true</optional>
                </dependency>

                <dependency>
                    <groupId>org.springframework.ai</groupId>
                    <artifactId>spring-ai-starter-model-openai</artifactId>
                    <scope>provided</scope>
                    <optional>true</optional>
                </dependency>

                <dependency>
                    <groupId>org.springframework.ai</groupId>
                    <artifactId>spring-ai-starter-model-ollama</artifactId>
                    <scope>provided</scope>
                    <optional>true</optional>
                </dependency>

                <dependency>
                    <groupId>org.springframework.ai</groupId>
                    <artifactId>spring-ai-starter-mcp-client-webflux</artifactId>
                    <scope>provided</scope>
                    <optional>true</optional>
                </dependency>

                <!-- 排除 langchain4j 依赖 -->
                <dependency>
                    <groupId>dev.langchain4j</groupId>
                    <artifactId>langchain4j-community-xinference</artifactId>
                    <scope>provided</scope>
                    <optional>true</optional>
                </dependency>

                <!-- 排除 plugins 模块依赖 -->
                <dependency>
                    <groupId>com.sinitek.sinicube</groupId>
                    <artifactId>sinitek-mind-plugins-vector-store-milvus</artifactId>
                    <scope>provided</scope>
                    <optional>true</optional>
                </dependency>

                <!-- 保留其他兼容 JDK 8 的依赖 -->
                <dependency>
                    <groupId>com.github.albfernandez</groupId>
                    <artifactId>juniversalchardet</artifactId>
                    <version>2.5.0</version>
                </dependency>

                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-mongodb</artifactId>
                </dependency>

                <dependency>
                    <groupId>org.postgresql</groupId>
                    <artifactId>postgresql</artifactId>
                    <scope>runtime</scope>
                </dependency>

                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-jdbc</artifactId>
                </dependency>

                <dependency>
                    <groupId>com.sinitek.sinicube</groupId>
                    <artifactId>sinitek-sirmapp</artifactId>
                </dependency>

                <dependency>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-bootstrap</artifactId>
                </dependency>

                <dependency>
                    <groupId>io.swagger.parser.v3</groupId>
                    <artifactId>swagger-parser</artifactId>
                    <version>2.1.12</version>
                </dependency>

                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-test</artifactId>
                    <scope>test</scope>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <!-- 禁用 spring-boot-maven-plugin -->
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>default</id>
                                <phase>none</phase>
                            </execution>
                            <execution>
                                <id>repackage</id>
                                <phase>none</phase>
                            </execution>
                        </executions>
                    </plugin>

                    <!-- 禁用 maven-assembly-plugin -->
                    <plugin>
                        <artifactId>maven-assembly-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>make-assembly</id>
                                <phase>none</phase>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <configuration>
                            <source>1.8</source>
                            <target>1.8</target>
                            <encoding>UTF-8</encoding>
                            <excludes>
                                <!-- 排除使用 Spring AI 的源文件 -->
                                <exclude>**/mind/common/**/*.java</exclude>
                                <exclude>**/mind/core/app/util/**/*.java</exclude>
                                <exclude>**/mind/core/chat/adapter/**/*.java</exclude>
                                <exclude>**/mind/core/workflow/**/*.java</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
