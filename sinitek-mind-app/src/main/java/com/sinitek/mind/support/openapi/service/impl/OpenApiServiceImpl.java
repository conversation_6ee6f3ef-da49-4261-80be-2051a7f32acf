package com.sinitek.mind.support.openapi.service.impl;

import com.sinitek.mind.common.constant.MindConstant;
import com.sinitek.mind.support.openapi.dto.ApiKeyInfoDTO;
import com.sinitek.mind.support.openapi.dto.ApiKeySearchResultDTO;
import com.sinitek.mind.support.openapi.dto.CreateApiKeyRequestDTO;
import com.sinitek.mind.support.openapi.dto.UpdateApiKeyRequestDTO;
import com.sinitek.mind.support.openapi.entity.OpenApi;
import com.sinitek.mind.support.openapi.repository.OpenApiRepository;
import com.sinitek.mind.support.openapi.service.IOpenApiService;
import com.sinitek.mind.support.openapi.util.ApiKeyConverter;
import com.sinitek.mind.support.openapi.util.ApiKeyUtil;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * OpenAPI服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Service
@Slf4j
@Transactional
public class OpenApiServiceImpl implements IOpenApiService {
    
    @Autowired
    private OpenApiRepository openApiRepository;

    @Autowired
    private IOperationLogService operationLogService;
    
    private static final int MAX_API_KEYS_PER_USER = 10;
    
    /**
     * 创建API密钥
     */
    @Override
    public String createApiKey(String teamId, String tmbId, CreateApiKeyRequestDTO request) {
        // 1. 检查API Key数量限制
        long count = openApiRepository.countByTmbIdAndAppId(tmbId, request.getAppId());
        if (count >= MAX_API_KEYS_PER_USER) {
            throw new BussinessException(true, "API密钥数量超过限制（最多" + MAX_API_KEYS_PER_USER + "个）");
        }
        
        // 2. 生成唯一API Key
        String apiKey = ApiKeyUtil.generateApiKey();
        
        // 3. 构建使用限制
        OpenApi.UsageLimit limit = null;
        if (request.getLimit() != null) {
            CreateApiKeyRequestDTO.LimitDTO limitDTO = request.getLimit();
            limit = OpenApi.UsageLimit.builder()
                .expiredTime(limitDTO.getExpiredTime())
                .maxUsagePoints(limitDTO.getMaxUsagePoints() != null ? limitDTO.getMaxUsagePoints() : -1L)
                .build();
        }
        
        // 4. 创建API Key记录
        OpenApi openApi = OpenApi.builder()
            .teamId(teamId)
            .tmbId(tmbId)
            .apiKey(apiKey)
            .appId(request.getAppId())
            .name(request.getName() != null ? request.getName() : "Api Key")
            .createTime(new Date())
            .usagePoints(0L)
            .limit(limit)
            .build();
        
        openApiRepository.save(openApi);

        operationLogService.addOperationLog(MindConstant.CREATE_API_KEY, Map.of("keyName", request.getName()));

        log.info("创建API密钥成功，用户: {}, 密钥: {}", tmbId, apiKey);
        return apiKey;
    }
    
    /**
     * 获取API密钥列表
     */
    @Override
    public List<ApiKeyInfoDTO> listApiKeys(String tmbId, String appId) {
        List<OpenApi> apiKeys;
        if (appId != null && !appId.isEmpty()) {
            apiKeys = openApiRepository.findByTmbIdAndAppId(tmbId, appId);
        } else {
            apiKeys = openApiRepository.findByTmbIdAndAppIdIsNull(tmbId);
        }
        
        return apiKeys.stream()
            .map(ApiKeyConverter::convertToApiKeyInfo)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取API密钥列表（FastGPT格式）
     */
    @Override
    public List<ApiKeySearchResultDTO> listApiKeysForFastGpt(String tmbId, String appId) {
        List<OpenApi> apiKeys;
        if (appId != null && !appId.isEmpty()) {
            apiKeys = openApiRepository.findByTmbIdAndAppId(tmbId, appId);
        } else {
            apiKeys = openApiRepository.findByTmbIdAndAppIdIsNull(tmbId);
        }
        
        return apiKeys.stream()
            .map(ApiKeyConverter::convertToApiKeySearchResult)
            .collect(Collectors.toList());
    }
    
    /**
     * 更新API密钥
     */
    @Override
    public void updateApiKey(UpdateApiKeyRequestDTO request) {
        OpenApi openApi = openApiRepository.findById(request.getId())
            .orElseThrow(() -> new BussinessException(true, "API密钥不存在"));
        
        // 更新基本信息
        if (request.getName() != null) {
            openApi.setName(request.getName());
        }
        
        // 更新使用限制
        if (request.getLimit() != null) {
            UpdateApiKeyRequestDTO.LimitDTO limitDTO = request.getLimit();
            OpenApi.UsageLimit limit = openApi.getLimit();
            if (limit == null) {
                limit = OpenApi.UsageLimit.builder().build();
            }
            
            if (limitDTO.getExpiredTime() != null) {
                limit.setExpiredTime(limitDTO.getExpiredTime());
            }
            if (limitDTO.getMaxUsagePoints() != null) {
                limit.setMaxUsagePoints(limitDTO.getMaxUsagePoints());
            }
            
            openApi.setLimit(limit);
        }
        
        openApiRepository.save(openApi);

        operationLogService.addOperationLog(MindConstant.UPDATE_API_KEY, Map.of("keyName", request.getName()));
        log.info("更新API密钥成功，ID: {}", request.getId());
    }
    
    /**
     * 删除API密钥
     */
    @Override
    public void deleteApiKey(String id) {
         OpenApi openApi = openApiRepository.findById(id).orElse(null);
        
        if (!openApiRepository.existsById(id)) {
            throw new BussinessException(true, "API密钥不存在");
        }
        
        openApiRepository.deleteById(id);
        
        if (openApi != null) {
            operationLogService.addOperationLog(MindConstant.DELETE_API_KEY, Map.of("keyName", openApi.getName()));
        }
        
        log.info("删除API密钥成功，ID: {}", id);
    }
    
    /**
     * 根据API密钥查找
     */
    @Override
    public Optional<OpenApi> findByApiKey(String apiKey) {
        return openApiRepository.findByApiKey(apiKey);
    }
    
    /**
     * 记录使用量
     */
    @Async
    @Override
    public void recordUsage(String apiKey, Long usagePoints) {
        try {
            openApiRepository.incrementUsagePoints(apiKey, usagePoints, new Date());
            log.debug("更新API密钥使用量: {}, +{} 点", apiKey, usagePoints);
        } catch (Exception e) {
            log.error("记录API密钥使用量失败: {}", apiKey, e);
        }
    }
    
    /**
     * 更新最后使用时间
     */
    @Async
    @Override
    public void updateLastUsedTime(String id) {
        try {
            openApiRepository.updateLastUsedTime(id, new Date());
        } catch (Exception e) {
            log.warn("更新API密钥最后使用时间失败: {}", id, e);
        }
    }
    

} 