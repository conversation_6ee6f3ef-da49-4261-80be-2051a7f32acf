package com.sinitek.mind.support.operationlog.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.common.util.PageUtil;
import com.sinitek.mind.core.app.model.SourceMemberDTO;
import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.operationlog.dto.AuditDTO;
import com.sinitek.mind.support.operationlog.dto.GetAuditListRequest;
import com.sinitek.mind.support.operationlog.enumerate.OperationLogEventEnum;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.team.util.TeamMemberTransformerUtil;
import com.sinitek.sirm.business.log.service.IBusinessLogExtService;
import com.sinitek.sirm.common.log.dto.BusinLogDTO;
import com.sinitek.sirm.common.log.dto.BusinLogQueryDTO;
import com.sinitek.sirm.common.log.enumrate.BusinLogType;
import com.sinitek.sirm.common.log.service.IBusinLogService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 操作记录接口实现类，使用框架的业务日志进行实现
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Slf4j
@Service
public class OperationLogServiceImpl implements IOperationLogService {

    private static final String METADATA_DEFAULT_NAME_KEY = "name";
    private static final String NO_EXIST_EVENT = "-1";

    @Autowired
    private IBusinessLogExtService businessLogExtService;

    @Autowired
    private IBusinLogService businLogService;

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IAccountService accountService;

    /* 字段对应关系
     * event-moduleName
     * params-desc(JSON格式)
     * 业务日志的operateType默认为1
     */
    @Override
    public void addOperationLog(BusinLogType event, Map<String, String> params) {
        // 存储orgId，获取时也是orgId
        String orgId = CurrentUserFactory.getOrgId();
        String tenantId = CurrentUserFactory.getTenantId();
        businessLogExtService.simpleSaveBusinessLog(orgId, tenantId, event.getModuleName(), event.getOperateType(),
            JSONUtil.toJsonStr(params));
    }

    @Override
    public PageResult<AuditDTO> findAudit(GetAuditListRequest request) {
        List<String> tmbIds = request.getTmbIds();
        List<String> events = request.getEvents();

        BusinLogQueryDTO queryDTO = new BusinLogQueryDTO();
        Tuple2<Integer, Integer> pageInfo = PageUtil.formatPageOffset(request);
        queryDTO.setPageIndex(pageInfo.getT1());
        queryDTO.setPageSize(pageInfo.getT2());

        if (CollUtil.isNotEmpty(tmbIds)) {
            queryDTO.setUserIds(tmbIds.toArray(new String[0]));
        }
        if (events == null) {
            // 说明选择了全部事件
            String[] operateTypeList = Arrays.stream(OperationLogEventEnum.values())
                .map(OperationLogEventEnum::getValue)
                .map(String::valueOf)
                .toArray(String[]::new);
            queryDTO.setOperateTypes(operateTypeList);
        } else if (CollUtil.isEmpty(events)) {
            // 一个都没有选择，但是需要去除框架中的事件，设置一个没有的数据即可
            queryDTO.setOperateTypes(new String[]{NO_EXIST_EVENT});
        }else {
            List<String> operateTypeList = transformEvent2OperateType(events);
            queryDTO.setOperateTypes(operateTypeList.toArray(new String[0]));
        }

        IPage<BusinLogDTO> businLogDTOIPage = businLogService.queryBusinLogs(queryDTO);
        List<AuditDTO> list = businLogDTOIPage.getRecords()
            .stream()
            .map(this::convertBusinLogDTO2AuditDTO)
            .collect(Collectors.toList());

        PageResult<AuditDTO> pageResult = new PageResult<>();
        pageResult.setTotal((int) businLogDTOIPage.getTotal());
        pageResult.setList(list);

        return pageResult;
    }

    private List<String> transformEvent2OperateType(List<String> eventList) {
        return eventList.stream()
            .map(event -> {
                for (OperationLogEventEnum logEventEnum : OperationLogEventEnum.values()) {
                    if (ObjectUtil.equal(logEventEnum.getName(), event)) {
                        return String.valueOf(logEventEnum.getValue());
                    }
                }
                return "-1";
            })
            .collect(Collectors.toList());
    }

    private AuditDTO convertBusinLogDTO2AuditDTO(BusinLogDTO dto) {
        AuditDTO auditDTO = new AuditDTO();
        auditDTO.set_id(String.valueOf(dto.getObjId()));
        auditDTO.setEvent(dto.getModuleName());
        auditDTO.setTimestamp(dto.getStartTime());

        // 存储时就是orgId
        Employee emp = orgService.getEmployeeById(dto.getUserId());
        if (emp == null) {
            emp = new Employee();
        }
        SourceMemberDTO memberDTO = new SourceMemberDTO();
        memberDTO.setName(emp.getEmpName());
        memberDTO.setAvatar(accountService.getAvatarByOrgId(emp.getId()));
        memberDTO.setStatus(TeamMemberTransformerUtil.status2gpt(String.valueOf(emp.getInservice())));
        auditDTO.setSourceMember(memberDTO);

        Map<String, Object> metadata = new HashMap<>();
        try {
            metadata = JSONUtil.toBean(dto.getDescription(), Map.class);
            metadata.put(METADATA_DEFAULT_NAME_KEY, emp.getEmpName());
        } catch (Exception e) {
            log.error("审计日志描述转换失败");
        }
        auditDTO.setMetadata(metadata);
        return auditDTO;
    }
}
