package com.sinitek.mind.core.app.service.impl;

import com.sinitek.mind.common.constant.MindConstant;
import com.sinitek.mind.core.app.dto.CreateMCPToolsDTO;
import com.sinitek.mind.core.app.dto.CreateMCPToolsResDTO;
import com.sinitek.mind.core.app.dto.UpdateMCPToolsDTO;
import com.sinitek.mind.core.app.dto.UpdateMCPToolsResDTO;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.McpToolConfig;
import com.sinitek.mind.core.app.model.SecretValue;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.core.app.repository.AppVersionRepository;
import com.sinitek.mind.core.app.service.IMCPToolsService;
import com.sinitek.mind.core.app.util.SecurityUtils;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MCPToolsServiceImpl implements IMCPToolsService {

    private final AppRepository appRepository;
    private final AppVersionRepository appVersionRepository;
    private final IAuthService authService;
    private final MongoTemplate mongoTemplate;
    private final IPermissionService permissionService;
    private final SecurityUtils securityUtils;
    private final IOperationLogService logService;

    /**
     * 创建MCP工具集
     * 对应原始的 create.ts 逻辑
     */
    @Transactional
    @Override
    public CreateMCPToolsResDTO createMCPTools(CreateMCPToolsDTO request, String authToken) {
        try {
            log.info("开始创建MCP工具集: {}", request.getName());

            // 1. 认证和权限验证
//            User currentUser = authService.authenticateUser(authToken);
//            if (currentUser == null) {
//                throw new BusinessException("认证失败");
//            }

            // 2. 检查团队应用限制
//            permissionService.checkTeamAppLimit(currentUser.getTeamId());

            // 3. 处理密钥存储
            Map<String, String> processedHeaderSecret = processSecretValues(request.getHeaderSecret());

            // 4. 创建主应用（MCP工具集） TODO user传值
            App mcpToolsApp = createMCPToolsApp(request, null, processedHeaderSecret);
            App savedMcpApp = appRepository.save(mcpToolsApp);

            // 5. 创建子工具应用 TODO user传值
            List<String> childToolIds = createChildToolApps(request.getToolList(), savedMcpApp, null);

            // 6. 更新主应用的子工具引用
            updateMCPToolsAppWithChildren(savedMcpApp.getId(), childToolIds);

            // 7. 记录操作日志
            logService.addOperationLog(MindConstant.CREATE_APP, Map.of("appName", savedMcpApp.getName(), "appType",  savedMcpApp.getType()));

            log.info("MCP工具集创建成功: {}, 子工具数量: {}", savedMcpApp.getId(), childToolIds.size());
            return CreateMCPToolsResDTO.success(savedMcpApp.getId(), childToolIds);

        } catch (Exception e) {
            log.error("创建MCP工具集失败: {}", e.getMessage(), e);
            throw new BussinessException("创建MCP工具集失败");
        }
    }

    @Override
    public UpdateMCPToolsResDTO updateMCPTools(UpdateMCPToolsDTO request, String authToken) {
        try {
            log.info("开始更新MCP工具集: {}", request.getAppId());

            // 1. 认证和权限验证
//            User currentUser = authService.authenticateUser(authToken);
//            if (currentUser == null) {
//                throw new BusinessException("认证失败");
//            }

            // 2. 获取现有应用
            App existingApp = appRepository.findById(request.getAppId())
                    .orElse(null);

            // 3. 权限检查
//            if (!hasPermission(currentUser, existingApp)) {
//                throw new BusinessException("无权限操作此应用");
//            }

            // 4. 处理密钥存储
            Map<String, String> processedHeaderSecret = processSecretValues(request.getHeaderSecret());

            // 5. 比较工具集数据变化
            boolean hasChanges = hasToolSetChanges(existingApp, request, processedHeaderSecret);

            // 6. 更新子工具 TODO user传值
            MCPChildrenUpdateResult updateResult = updateMCPChildrenTools(
                    existingApp, request.getToolList(), null);

            // 7. 更新主应用
            if (hasChanges || updateResult.hasChanges()) {
                updateMCPToolsApp(existingApp, request, processedHeaderSecret, updateResult.getAllChildIds());
            }

            // 8. 记录操作日志
            logService.addOperationLog(MindConstant.UPDATE_APP_INFO, Map.of("appName", existingApp.getName(), "appType",  existingApp.getType()));

            log.info("MCP工具集更新成功: {}", request.getAppId());
            return UpdateMCPToolsResDTO.success(
                    request.getAppId(),
                    updateResult.getUpdatedToolIds(),
                    updateResult.getCreatedToolIds(),
                    updateResult.getDeletedToolIds()
            );

        } catch (Exception e) {
            log.error("更新MCP工具集失败: {}", e.getMessage(), e);
            throw new BussinessException("更新MCP工具集失败");
        }
    }

    /**
     * 处理密钥值
     */
    private Map<String, String> processSecretValues(Map<String, SecretValue> headerSecret) {
        if (headerSecret == null || headerSecret.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, String> processed = new HashMap<>();
        for (Map.Entry<String, SecretValue> entry : headerSecret.entrySet()) {
            SecretValue secretValue = entry.getValue();
            if (secretValue != null) {
                // 如果有明文值，则加密存储；否则使用已加密的值
                String storedValue = StringUtils.isNotBlank(secretValue.getValue())
                        ? securityUtils.encryptSecret(secretValue.getValue())
                        : secretValue.getSecret();
                processed.put(entry.getKey(), storedValue);
            }
        }
        return processed;
    }

    /**
     * 创建MCP工具集应用
     */
    private App createMCPToolsApp(CreateMCPToolsDTO request, Object user, Map<String, String> headerSecret) {
        App app = new App();
        app.setId(generateId());
//        app.setUserId(user.getId());
//        app.setTeamId(user.getTeamId());
//        app.setTmbId(user.getTmbId());
        app.setName(request.getName());
        app.setAvatar(request.getAvatar());
        app.setIntro(request.getIntro());

        app.setType(AppTypeEnum.MCP_TOOLS.getValue());
        app.setParentId(request.getParentId());
//        app.setCreateTime(LocalDateTime.now());
        app.setUpdateTime(new Date());

        // 设置MCP工具集特有配置
        Map<String, Object> mcpConfig = new HashMap<>();
        mcpConfig.put("url", request.getUrl());
        mcpConfig.put("headerSecret", headerSecret);
//        app.setMcpToolsConfig(mcpConfig);

        return app;
    }

    /**
     * 创建子工具应用
     */
    private List<String> createChildToolApps(List<McpToolConfig> toolList, App parentApp, Object user) {
        List<String> childIds = new ArrayList<>();

        for (McpToolConfig toolConfig : toolList) {
            App childApp = createChildToolApp(toolConfig, parentApp, user);
            App savedChild = appRepository.save(childApp);
            childIds.add(savedChild.getId());
        }

        return childIds;
    }

    /**
     * 创建单个子工具应用
     */
    private App createChildToolApp(McpToolConfig toolConfig, App parentApp, Object user) {
        App childApp = new App();
        childApp.setId(generateId());
//        childApp.setUserId(user.getId());
//        childApp.setTeamId(user.getTeamId());
//        childApp.setTmbId(user.getTmbId());
        childApp.setName(toolConfig.getName());
        childApp.setIntro(toolConfig.getDescription());
        childApp.setType(AppTypeEnum.PLUGIN.getValue());
        childApp.setParentId(parentApp.getId());
//        childApp.setCreateTime(new Date());
        childApp.setUpdateTime(new Date());

        // 设置工具配置
        Map<String, Object> toolConfigMap = new HashMap<>();
        toolConfigMap.put("name", toolConfig.getName());
        toolConfigMap.put("description", toolConfig.getDescription());
        toolConfigMap.put("inputSchema", toolConfig.getInputSchema());
//        childApp.setToolConfig(toolConfigMap);

        return childApp;
    }

    /**
     * 更新MCP工具集的子工具引用
     */
    private void updateMCPToolsAppWithChildren(String appId, List<String> childIds) {
        Query query = new Query(Criteria.where("id").is(appId));
        Update update = new Update()
                .set("childToolIds", childIds)
                .set("updateTime", LocalDateTime.now());
        mongoTemplate.updateFirst(query, update, App.class);
    }

    /**
     * 检查工具集数据是否有变化
     */
    private boolean hasToolSetChanges(App existingApp, UpdateMCPToolsDTO request,
                                      Map<String, String> processedHeaderSecret) {
//        Map<String, Object> existingConfig = existingApp.getMcpToolsConfig();
        Map<String, Object> existingConfig = new HashMap<>();
        if (existingConfig == null) {
            return true;
        }

        String existingUrl = (String) existingConfig.get("url");
        Map<String, String> existingHeaderSecret = (Map<String, String>) existingConfig.get("headerSecret");

        return !Objects.equals(existingUrl, request.getUrl()) ||
                !Objects.equals(existingHeaderSecret, processedHeaderSecret);
    }

    /**
     * 更新子工具
     */
    private MCPChildrenUpdateResult updateMCPChildrenTools(App parentApp, List<McpToolConfig> newToolList, Object user) {
        // 获取现有子工具
        List<String> existingChildIds = parentApp.getChildToolIds() != null ?
                parentApp.getChildToolIds() : new ArrayList<>();
        List<App> existingChildren = appRepository.findAllById(existingChildIds);

        Map<String, App> existingToolMap = existingChildren.stream()
                .collect(Collectors.toMap(app -> (String) app.getToolConfig().get("name"), app -> app));

        List<String> updatedToolIds = new ArrayList<>();
        List<String> createdToolIds = new ArrayList<>();
        List<String> deletedToolIds = new ArrayList<>();
        List<String> allChildIds = new ArrayList<>();

        // 处理新工具列表
        for (McpToolConfig newTool : newToolList) {
            App existingTool = existingToolMap.get(newTool.getName());

            if (existingTool != null) {
                // 更新现有工具
                if (hasToolConfigChanges(existingTool, newTool)) {
                    updateChildToolApp(existingTool, newTool);
                    updatedToolIds.add(existingTool.getId());
                }
                allChildIds.add(existingTool.getId());
                existingToolMap.remove(newTool.getName());
            } else {
                // 创建新工具
                App newChildApp = createChildToolApp(newTool, parentApp, user);
                App savedChild = appRepository.save(newChildApp);
                createdToolIds.add(savedChild.getId());
                allChildIds.add(savedChild.getId());
            }
        }

        // 删除不再需要的工具
        for (App toolToDelete : existingToolMap.values()) {
            appRepository.delete(toolToDelete);
            deletedToolIds.add(toolToDelete.getId());
        }

        return new MCPChildrenUpdateResult(updatedToolIds, createdToolIds, deletedToolIds, allChildIds);
    }

    /**
     * 检查工具配置是否有变化
     */
    private boolean hasToolConfigChanges(App existingTool, McpToolConfig newConfig) {
        Map<String, Object> existingConfig = existingTool.getToolConfig();
        if (existingConfig == null) {
            return true;
        }

        return !Objects.equals(existingConfig.get("description"), newConfig.getDescription()) ||
                !Objects.equals(existingConfig.get("inputSchema"), newConfig.getInputSchema());
    }

    /**
     * 更新子工具应用
     */
    private void updateChildToolApp(App childApp, McpToolConfig newConfig) {
        childApp.setIntro(newConfig.getDescription());
        childApp.setUpdateTime(new Date());

        Map<String, Object> toolConfig = childApp.getToolConfig();
        if (toolConfig == null) {
            toolConfig = new HashMap<>();
        }
        toolConfig.put("description", newConfig.getDescription());
        toolConfig.put("inputSchema", newConfig.getInputSchema());
        childApp.setToolConfig(toolConfig);

        appRepository.save(childApp);
    }

    /**
     * 更新MCP工具集应用
     */
    private void updateMCPToolsApp(App app, UpdateMCPToolsDTO request,
                                   Map<String, String> headerSecret, List<String> childIds) {
        app.setUpdateTime(new Date());
        app.setChildToolIds(childIds);

        Map<String, Object> mcpConfig = app.getMcpToolsConfig();
        if (mcpConfig == null) {
            mcpConfig = new HashMap<>();
        }
        mcpConfig.put("url", request.getUrl());
        mcpConfig.put("headerSecret", headerSecret);
        app.setMcpToolsConfig(mcpConfig);

        appRepository.save(app);
    }

    /**
     * 检查用户权限
     */
//    private boolean hasPermission(User user, App app) {
//        return Objects.equals(user.getId(), app.getUserId()) ||
//                Objects.equals(user.getTeamId(), app.getTeamId());
//    }


    /**
     * 生成ID
     */
    private String generateId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 子工具更新结果
     */
    private static class MCPChildrenUpdateResult {
        private final List<String> updatedToolIds;
        private final List<String> createdToolIds;
        private final List<String> deletedToolIds;
        private final List<String> allChildIds;

        public MCPChildrenUpdateResult(List<String> updatedToolIds, List<String> createdToolIds,
                                       List<String> deletedToolIds, List<String> allChildIds) {
            this.updatedToolIds = updatedToolIds;
            this.createdToolIds = createdToolIds;
            this.deletedToolIds = deletedToolIds;
            this.allChildIds = allChildIds;
        }

        public boolean hasChanges() {
            return !updatedToolIds.isEmpty() || !createdToolIds.isEmpty() || !deletedToolIds.isEmpty();
        }

        public List<String> getUpdatedToolIds() { return updatedToolIds; }
        public List<String> getCreatedToolIds() { return createdToolIds; }
        public List<String> getDeletedToolIds() { return deletedToolIds; }
        public List<String> getAllChildIds() { return allChildIds; }
    }
}
