package com.sinitek.mind.core.workflow.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.chat.enumerate.ChatItemValueType;
import com.sinitek.mind.core.chat.model.AIChatItemValueItemType;
import com.sinitek.mind.core.chat.model.ChatHistoryItemResType;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.NodeOutputItemType;
import com.sinitek.mind.core.dataset.model.DebugResponse;
import com.sinitek.mind.core.workflow.dispatch.NodeService;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.SseResponseEventEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.model.sse.FlowNodeStatusResponse;
import com.sinitek.mind.core.workflow.model.sse.WorkflowDurationResponse;
import com.sinitek.mind.core.workflow.service.IWorkflowService;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.support.outlink.entity.OutLink;
import com.sinitek.sirm.common.limit.annotation.SiniCubeLimiter;
import com.sinitek.sirm.common.limit.enumerate.SiniCubeLimiterMode;
import com.sinitek.sirm.common.spring.SpringFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowServiceImpl implements IWorkflowService {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final NodeService nodeService;

    @Override
    public CompletableFuture<DispatchFlowResponse> dispatchWorkFlow(ChatDispatchProps props) {
        ThreadPoolTaskExecutor threadExecutor = SpringFactory.getBean(ThreadPoolTaskExecutor.class);
        return CompletableFuture.supplyAsync(() -> {
            try {
                return executeWorkFlow(props);
            } catch (Exception e) {
                log.error("工作流执行失败", e);
                throw new RuntimeException(e);
            }
        }, threadExecutor);
    }

    @Override
    @SiniCubeLimiter(mode = SiniCubeLimiterMode.SERVER, rate = "#outLink?.Limit?.QPM ?: 100", interval = "60", blocking = false)
    public CompletableFuture<DispatchFlowResponse> dispatchWorkFlowWithQpmLimit(ChatDispatchProps dispatchProps, OutLink outLink) {
        return dispatchWorkFlow(dispatchProps);
    }

    @Override
    public Map<String, Object> getSystemVariables(ChatDispatchProps props) {
        Map<String, Object> variables = new HashMap<>();

        // 处理聊天配置变量
        if (props.getChatConfig() != null && props.getChatConfig().getVariables() != null) {
            props.getChatConfig().getVariables().forEach(variable -> {
                Object value = WorkflowUtil.valueTypeFormat(variable.getDefaultValue(), variable.getValueType());
                variables.put(variable.getKey(), value);
            });
        }

        // 添加系统变量
        variables.put("userId", props.getUid());
        variables.put("appId", props.getRunningAppInfo().getId());
        variables.put("chatId", props.getChatId());
        variables.put("responseChatItemId", props.getResponseChatItemId());
        variables.put("histories", props.getHistories());
        variables.put("cTime", getSystemTime(props.getTimezone()));

        return variables;
    }

    private DispatchFlowResponse executeWorkFlow(ChatDispatchProps data) {
        ThreadPoolTaskExecutor threadExecutor = SpringFactory.getBean(ThreadPoolTaskExecutor.class);

        long startTime = System.currentTimeMillis();
        // 解构参数
        SseEmitter res = data.getRes();
        List<RuntimeNodeItemType> runtimeNodes = Optional.ofNullable(data.getRuntimeNodes()).orElse(new ArrayList<>());
        List<RuntimeEdgeItemType> runtimeEdges = Optional.ofNullable(data.getRuntimeEdges()).orElse(new ArrayList<>());
        List<ChatItemType> histories = Optional.ofNullable(data.getHistories()).orElse(new ArrayList<>());
        Map<String, Object> variables = new HashMap<>(Optional.ofNullable(data.getVariables()).orElse(new HashMap<>()));
        String timezone = data.getTimezone();
        ExternalProviderType externalProvider = data.getExternalProvider();
        boolean stream = data.isStream();
        Boolean retainDatasetCite = data.getRetainDatasetCite();
        String version = Optional.ofNullable(data.getVersion()).orElse("v1");
        Boolean responseDetail = data.getResponseDetail();
        Boolean responseAllData = data.getResponseAllData();

        WorkflowUtil.rewriteRuntimeWorkFlow(data.getRuntimeNodes(), data.getRuntimeEdges());
        // 初始化深度和自动增加深度，避免无限嵌套
        Integer workflowDispatchDeep = data.getWorkflowDispatchDeep();
        if (workflowDispatchDeep == null) {
            data.setWorkflowDispatchDeep(1);
            workflowDispatchDeep = 1;
        } else {
            data.setWorkflowDispatchDeep(workflowDispatchDeep + 1);
            workflowDispatchDeep += 1;
        }

        boolean isRootRuntime = workflowDispatchDeep == 1;
        // 防止无限嵌套
        if (data.getWorkflowDispatchDeep() > 20) {
            return DispatchFlowResponse.builder()
                    .flowResponses(new ArrayList<>())
                    .flowUsage(new ArrayList<>())
                    .debugResponse(DebugResponse.builder()
                            .finishedNodes(new ArrayList<>())
                            .finishedEdges(new ArrayList<>())
                            .nextStepRunNodes(new ArrayList<>())
                            .build())
                    .runTimes(1)
                    .assistantResponses(null)
                    .toolResponses(null)
                    .newVariables(WorkflowUtil.removeSystemVariable(data.getVariables(), data.getExternalProvider().getExternalWorkflowVariables()))
                    .durationSeconds(0.0)
                    .build();
        }
        int workflowRunTimes = 0;

        // 初始化
        if (isRootRuntime) {
            // 设置SSE响应头
            // 这部分并不需要
//            if (res != null) {
//                setupSseHeaders(res);
//            }
//
//            if (stream && res != null) {
//                setupStreamResponse(res, data);
//            }

            // 添加系统变量
            Map<String, Object> systemVariables = getSystemVariables(data);
            variables.putAll(systemVariables);
            if (externalProvider.getExternalWorkflowVariables() != null) {
                variables.putAll(externalProvider.getExternalWorkflowVariables());
            }
        }

        // 初始化响应数据
        WorkflowExecutionContext context = new WorkflowExecutionContext();
        context.setChatResponses(new ArrayList<>());
        context.setChatAssistantResponse(new ArrayList<>());
        context.setChatNodeUsages(new ArrayList<>());
        context.setDebugNextStepRunNodes(new ArrayList<>());
        context.setSystemMemories(new HashMap<>());
        context.setVariables(variables);
        context.setHistories(histories);
        context.setWorkflowRunTimes(workflowRunTimes);
        context.setProps(data);
        context.setQuery(data.getQuery());

        try {
            // 开始处理入口节点
            List<RuntimeNodeItemType> entryNodes = runtimeNodes.stream()
                    .filter(RuntimeNodeItemType::getIsEntry)
                    .toList();

            // 重置入口状态
            runtimeNodes.forEach(item -> {
                // 交互节点将使用"isEntry"，不需要更新
                if (!FlowNodeTypeEnum.USER_SELECT.getValue().equals(item.getFlowNodeType()) &&
                        !FlowNodeTypeEnum.FORM_INPUT.getValue().equals(item.getFlowNodeType()) &&
                        !FlowNodeTypeEnum.TOOLS.getValue().equals(item.getFlowNodeType())) {
                    item.setIsEntry(false);
                }
            });

            // 并行执行入口节点
            List<CompletableFuture<List<RuntimeNodeItemType>>> entryFutures = entryNodes.stream()
                    .map(node -> CompletableFuture.supplyAsync(() ->
                            checkNodeCanRun(node, new HashSet<>(), runtimeNodes, runtimeEdges, context), threadExecutor))
                    .toList();

            CompletableFuture.allOf(entryFutures.toArray(new CompletableFuture[0])).join();

            // 尝试运行插件输出模块
            Optional<RuntimeNodeItemType> pluginOutputModule = runtimeNodes.stream()
                    .filter(item -> FlowNodeTypeEnum.PLUGIN_OUTPUT.getValue().equals(item.getFlowNodeType()))
                    .findFirst();

            if (pluginOutputModule.isPresent() && !"debug".equals(data.getMode())) {
                nodeRunWithActive(pluginOutputModule.get(), runtimeNodes, runtimeEdges, context);
            }

            // 处理交互节点
            WorkflowInteractiveResponseType interactiveResult = null;
            if (context.getNodeInteractiveResponse() != null) {
                AIChatItemValueItemType interactiveAssistant = handleInteractiveResult(
                        context.getNodeInteractiveResponse().getEntryNodeIds(),
                        context.getNodeInteractiveResponse().getInteractiveResponse(),
                        runtimeNodes, runtimeEdges, data, isRootRuntime
                );
                if (isRootRuntime) {
                    context.getChatAssistantResponse().add(interactiveAssistant);
                }
                interactiveResult = (WorkflowInteractiveResponseType)interactiveAssistant.getInteractive();
            }

            double durationSeconds = (System.currentTimeMillis() - startTime) / 1000.0;

            // 输出工作流执行时长
            if (isRootRuntime && stream) {
                SseEmitter res1 = data.getRes();
                sendWorkflowDuration(data, durationSeconds);
            }

            return DispatchFlowResponse.builder()
                    .flowResponses(context.getChatResponses())
                    .flowUsage(context.getChatNodeUsages())
                    .debugResponse(DebugResponse.builder()
                            .finishedNodes(runtimeNodes)
                            .finishedEdges(runtimeEdges)
                            .nextStepRunNodes(context.getDebugNextStepRunNodes())
                            .build())
                    .workflowInteractiveResponse(interactiveResult)
                    .runTimes(context.getWorkflowRunTimes())
                    .assistantResponses(mergeAssistantResponseAnswerText(context.getChatAssistantResponse()))
                    .toolResponses(context.getToolRunResponse())
                    .newVariables(WorkflowUtil.removeSystemVariable(context.getVariables(), externalProvider.getExternalWorkflowVariables()))
                    .system_memories(context.getSystemMemories().isEmpty() ? null : context.getSystemMemories())
                    .durationSeconds(durationSeconds)
                    .build();
        } catch (Exception error) {
            log.error("工作流执行异常", error);
            return null;
        }

    }

    /**
     * 检查节点是否可以运行
     */
    private List<RuntimeNodeItemType> checkNodeCanRun(
            RuntimeNodeItemType node,
            Set<String> skippedNodeIdList,
            List<RuntimeNodeItemType> runtimeNodes,
            List<RuntimeEdgeItemType> runtimeEdges,
            WorkflowExecutionContext context) {

        if (context.getProps().getRes() == null ||
                context.getProps().getMaxRunTimes() <= 0) {
            return new ArrayList<>();
        }
        ThreadPoolTaskExecutor threadExecutor = SpringFactory.getBean(ThreadPoolTaskExecutor.class);

        // 线程让步
        surrenderProcess();

        log.info("运行节点: {}, 最大运行次数: {}, 应用ID: {}",
                node.getName(), context.getProps().getMaxRunTimes(), context.getProps().getRunningAppInfo().getId());

        // 通过边获取节点运行状态
        String status = checkNodeRunStatus(node, runtimeEdges);

        NodeRunResult nodeRunResult = null;
        if ("run".equals(status)) {
            nodeRunBeforeHook(node, runtimeEdges);
            log.info("[dispatchWorkFlow] nodeRunWithActive: {}", node.getName());
            nodeRunResult = nodeRunWithActive(node, runtimeNodes, runtimeEdges, context);
        } else if ("skip".equals(status) && !skippedNodeIdList.contains(node.getNodeId())) {
            nodeRunBeforeHook(node, runtimeEdges);
            context.getProps().setMaxRunTimes(context.getProps().getMaxRunTimes() - 0.1);
            skippedNodeIdList.add(node.getNodeId());
            log.info("[dispatchWorkFlow] nodeRunWithSkip: {}", node.getName());
            nodeRunResult = nodeRunWithSkip(node, runtimeEdges);
        }

        if (nodeRunResult == null) {
            return new ArrayList<>();
        }

        // 特殊情况：通过skipEdges可以判断是运行了分支节点
        List<String> skipEdges = (List<String>) nodeRunResult.getResult().get("skipHandleId");
        if (skipEdges != null && !skipEdges.isEmpty()) {
            skippedNodeIdList.add(node.getNodeId());
        }

        // 当前版本只允许同时有一个交互节点
        Object interactiveResponse = nodeRunResult.getResult().get("interactive");
        if (interactiveResponse != null) {
            pushStore(nodeRunResult.getNode(), nodeRunResult.getResult(), context);

            if ("debug".equals(context.getProps().getMode())) {
                context.getDebugNextStepRunNodes().add(nodeRunResult.getNode());
            }

            context.setNodeInteractiveResponse(NodeInteractiveResponse.builder()
                    .entryNodeIds(List.of(nodeRunResult.getNode().getNodeId()))
                    .interactiveResponse((InteractiveNodeType) interactiveResponse)
                    .build());
            return new ArrayList<>();
        }

        // 在运行结束时更新节点输出并获取下一个节点
        NodeOutputResult outputResult = nodeOutput(nodeRunResult.getNode(), nodeRunResult.getResult(),
                runtimeNodes, runtimeEdges, context);

        List<RuntimeNodeItemType> nextStepActiveNodes = outputResult.getNextStepActiveNodes();
        List<RuntimeNodeItemType> nextStepSkipNodes = outputResult.getNextStepSkipNodes();

        // 去除重复节点（确保节点只执行一次）
        nextStepActiveNodes = removeDuplicateNodes(nextStepActiveNodes);
        nextStepSkipNodes = removeDuplicateNodes(nextStepSkipNodes);

        // 运行下一个节点（先运行run的，再运行skip的）
        List<CompletableFuture<List<RuntimeNodeItemType>>> activeFutures = nextStepActiveNodes.stream()
                .map(nextNode -> CompletableFuture.supplyAsync(() ->
                        checkNodeCanRun(nextNode, new HashSet<>(skippedNodeIdList), runtimeNodes, runtimeEdges, context), threadExecutor))
                .toList();

        List<RuntimeNodeItemType> nextStepActiveNodesResults = activeFutures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .toList();

        // 如果已经active运行过，不再执行skip（active中有闭环）
        nextStepSkipNodes = nextStepSkipNodes.stream()
                .filter(node1 -> nextStepActiveNodesResults.stream()
                        .noneMatch(item -> item.getNodeId().equals(node1.getNodeId())))
                .collect(Collectors.toList());

        List<CompletableFuture<List<RuntimeNodeItemType>>> skipFutures = nextStepSkipNodes.stream()
                .map(nextNode -> CompletableFuture.supplyAsync(() ->
                        checkNodeCanRun(nextNode, skippedNodeIdList, runtimeNodes, runtimeEdges, context), threadExecutor))
                .toList();

        List<RuntimeNodeItemType> nextStepSkipNodesResults = skipFutures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .toList();

        if (context.getProps().getRes() != null) {
            log.info("请求已关闭, 应用ID: {}, 节点ID: {}, 节点名称: {}",
                    context.getProps().getRunningAppInfo().getId(), node.getNodeId(), node.getName());
            return new ArrayList<>();
        }

        List<RuntimeNodeItemType> result = new ArrayList<>();
        result.addAll(nextStepActiveNodes);
        result.addAll(nextStepSkipNodes);
        result.addAll(nextStepActiveNodesResults);
        result.addAll(nextStepSkipNodesResults);
        return result;
    }

    private NodeRunResult nodeRunWithSkip(RuntimeNodeItemType node, List<RuntimeEdgeItemType> runtimeEdges) {
        // 跳过节点运行逻辑
        List<String> skipHandleIds = runtimeEdges.stream()
                .filter(item -> node.getNodeId().equals(item.getSource()))
                .map(RuntimeEdgeItemType::getSourceHandle)
                .collect(Collectors.toList());

        Map<String, Object> result = new HashMap<>();
        result.put("skipHandleId", skipHandleIds);

        return NodeRunResult.builder()
                .node(node)
                .runStatus("skip")
                .result(result)
                .build();
    }

    private NodeRunResult nodeRunWithActive(RuntimeNodeItemType node, List<RuntimeNodeItemType> runtimeNodes,
                                            List<RuntimeEdgeItemType> runtimeEdges, WorkflowExecutionContext context) {
        // 推送运行状态消息
        if (Boolean.TRUE.equals(node.getShowStatus()) && !Boolean.TRUE.equals(context.getProps().getIsToolCall())) {
            if (context.getProps().getWorkflowStreamResponse() != null) {
                FlowNodeStatusResponse flowNodeStatusResponse = FlowNodeStatusResponse.builder()
                        .status("running")
                        .name(node.getName())
                        .build();

                WorkflowStreamResponse streamResponse =
                        WorkflowStreamResponse.builder()
                                .event(SseResponseEventEnum.FLOW_NODE_STATUS.getValue())
                                .data(flowNodeStatusResponse)
                                .build();
                context.getProps().getWorkflowStreamResponse().accept(streamResponse);
            }
        }

        long startTime = System.currentTimeMillis();

        // 获取节点运行参数
        Map<String, Object> params = getNodeRunParams(node, runtimeNodes, context.getVariables());

        // 构建调度数据
        ModuleDispatchProps dispatchData = ModuleDispatchProps.builder()
                .res(context.getProps().getRes())
                .requestOrigin(context.getProps().getRequestOrigin())
                .runningAppInfo(context.getProps().getRunningAppInfo())
                .runningUserInfo(context.getProps().getRunningUserInfo())
                .uid(context.getProps().getUid())
                .chatId(context.getProps().getChatId())
                .responseChatItemId(context.getProps().getResponseChatItemId())
                .chatConfig(context.getProps().getChatConfig())
                .lastInteractive(context.getProps().getLastInteractive())
                .maxRunTimes(context.getProps().getMaxRunTimes())
                .isToolCall(context.getProps().getIsToolCall())
                .workflowDispatchDeep(context.getProps().getWorkflowDispatchDeep())
                .version(context.getProps().getVersion())
                .responseAllData(context.getProps().getResponseAllData())
                .responseDetail(context.getProps().getResponseDetail())
                .variables(context.getVariables())
                .histories(context.getHistories())
                .timezone(context.getProps().getTimezone())
                .externalProvider(context.getProps().getExternalProvider())
                .stream(context.getProps().isStream())
                .workflowStreamResponse(context.getProps().getWorkflowStreamResponse())
                .retainDatasetCite(context.getProps().getRetainDatasetCite())
                .node(node)
                .runtimeNodes(runtimeNodes)
                .runtimeEdges(runtimeEdges)
                .params(params)
                .query(context.getQuery())
                .mode("debug".equals(context.getProps().getMode()) ? "test" : context.getProps().getMode())
                .build();

        // 运行模块
        Map<String, Object> dispatchRes = new HashMap<>();
        try {
            // 这里应该调用具体的节点处理器
            dispatchRes = executeNodeLogic(node, dispatchData);
        } catch (Exception error) {
            // 获取输出边的源句柄
            List<String> skipHandleIds = runtimeEdges.stream()
                    .filter(item -> node.getNodeId().equals(item.getSource()))
                    .map(RuntimeEdgeItemType::getSourceHandle)
                    .collect(Collectors.toList());

            // TODO getErrText(error)方法
            context.setToolRunResponse(error.getMessage());

            // 跳过所有边并返回错误
            // TODO formatHttpError
            dispatchRes.put("responseData", Map.of("error", ""));
            dispatchRes.put("skipHandleId", skipHandleIds);
        }

        // TODO 剩余参数 ；格式化响应数据，添加模块名称和模块类型
        ChatHistoryItemResType formatResponseData = null;
        if (dispatchRes.get("responseData") != null) {
            Map<String, Object> responseData = (Map<String, Object>) dispatchRes.get("responseData");
            if (!responseData.isEmpty()) {
                formatResponseData = new ChatHistoryItemResType();
                formatResponseData.setId(WorkflowUtil.getNanoid(21));
                formatResponseData.setNodeId(node.getNodeId());
                formatResponseData.setModuleName(node.getName());
                formatResponseData.setModel(responseData.get("model") == null ? "" : responseData.get("model").toString());
                formatResponseData.setModuleType(node.getFlowNodeType());
                formatResponseData.setRunningTime((System.currentTimeMillis() - startTime) / 1000.0);

                // 处理 headers
                if (responseData.get("headers") == null) {
                    formatResponseData.setHeaders(new HashMap<>());
                } else {
                    formatResponseData.setHeaders((Map) responseData.get("headers"));
                }

                // 处理 totalPoints
                if (responseData.get("totalPoints") == null) {
                    formatResponseData.setTotalPoints(0.0);
                } else {
                    Integer totalPoints = (Integer) responseData.get("totalPoints");
                    formatResponseData.setTotalPoints(totalPoints.doubleValue());
                }

                // 处理 httpResult
                if (responseData.get("httpResult") == null) {
                    formatResponseData.setHttpResult(new HashMap<>());
                } else {
                    formatResponseData.setHttpResult((Map) responseData.get("httpResult"));
                }

                // 处理 params
                if (responseData.get("params") == null) {
                    formatResponseData.setParams(new HashMap<>());
                } else {
                    formatResponseData.setParams((Map) responseData.get("params"));
                }

                // 处理 pluginOutput
                if (responseData.get("pluginOutput") == null) {
                    formatResponseData.setPluginOutput(new HashMap<>());
                } else {
                    formatResponseData.setPluginOutput((Map) responseData.get("pluginOutput"));
                }
            }

            // 复制响应数据
            Map<String, Object> nodeResponse = (Map<String, Object>) dispatchRes.get("nodeResponse");
//            if (nodeResponse.get("query") != null) {
//                formatResponseData.setQuery((String) nodeResponse.get("query"));
//            }
//            if (nodeResponse.get("response") != null) {
////                formatResponseData.setResponse((String) nodeResponse.get("response"));
//            }
            if (nodeResponse != null && nodeResponse.get("error") != null) {
                formatResponseData.setError(nodeResponse.get("error"));
            }
        }

        // 响应节点响应
        if ("v2".equals(context.getProps().getVersion()) &&
                !Boolean.TRUE.equals(context.getProps().getIsToolCall()) &&
                context.getProps().getWorkflowDispatchDeep() == 1 &&
                formatResponseData != null) {

            if (context.getProps().getWorkflowStreamResponse() != null) {
                ChatHistoryItemResType responseData = Boolean.TRUE.equals(context.getProps().getResponseAllData()) ?
                        formatResponseData :
                        WorkflowUtil.filterPublicNodeResponseData(List.of(formatResponseData),
                                Boolean.TRUE.equals(context.getProps().getResponseDetail())).get(0);

                WorkflowStreamResponse streamResponse =
                        WorkflowStreamResponse.builder()
                                .event(SseResponseEventEnum.FLOW_NODE_RESPONSE.getValue())
                                .data(responseData)
                                .build();
                context.getProps().getWorkflowStreamResponse().accept(streamResponse);
            }
        }

        // 添加输出默认值
        if (node.getOutputs() != null) {
            Map<String, Object> finalDispatchRes = dispatchRes;
            node.getOutputs().forEach(item -> {
                if (Boolean.TRUE.equals(item.getRequired()) &&
                        finalDispatchRes.get(item.getKey()) == null &&
                        item.getDefaultValue() != null) {
                    finalDispatchRes.put(item.getKey(), WorkflowUtil.valueTypeFormat(item.getDefaultValue(), item.getValueType()));
                }
            });
        }

        // 更新新变量
        Map<String, Object> newVariables = (Map<String, Object>) dispatchRes.get("newVariables");
        if (newVariables != null) {
            context.getVariables().putAll(newVariables);
        }

        // 错误处理
        if (formatResponseData != null && formatResponseData.getError() != null) {
            log.warn("工作流错误: {}", formatResponseData.getError());
        }

        Map<String, Object> result = new HashMap<>(dispatchRes);
        result.put("nodeResponse", formatResponseData);

        return NodeRunResult.builder()
                .node(node)
                .runStatus("run")
                .result(result)
                .build();
    }

    private AIChatItemValueItemType handleInteractiveResult(List<String> entryNodeIds, Object interactiveResponse,
                                                    List<RuntimeNodeItemType> runtimeNodes, List<RuntimeEdgeItemType> runtimeEdges,
                                                    ChatDispatchProps props, boolean isRootRuntime) {

        List<NodeOutputItemType> nodeOutputs = new ArrayList<>();
        runtimeNodes.forEach(item -> {
            item.getOutputs().forEach(itemOutput -> {
                if (Boolean.TRUE.equals(itemOutput.getValue())) {
                    NodeOutputItemType nodeOutputItemType = new NodeOutputItemType();
                    nodeOutputItemType.setKey(itemOutput.getKey());
                    nodeOutputItemType.setValue(itemOutput.getValue());
                    nodeOutputItemType.setNodeId(item.getNodeId());
                    nodeOutputs.add(nodeOutputItemType);
                }
            });
        });
        List<RuntimeEdgeItemType> me = runtimeEdges.stream().map(item -> {
            RuntimeEdgeItemType runtimeEdgeItemType = new RuntimeEdgeItemType();
            BeanUtils.copyProperties(item, runtimeEdgeItemType);
            if (entryNodeIds.contains(item.getTarget())) {
                runtimeEdgeItemType.setStatus("active");
            }
            return runtimeEdgeItemType;
        }).toList();
        WorkflowInteractiveResponseType interactiveResult = WorkflowInteractiveResponseType.builder()
                .nodeResponse((InteractiveNodeType) interactiveResponse)
                .entryNodeIds(entryNodeIds)
                .nodeOutputs(nodeOutputs)
                .memoryEdges(me)
                .build();

        if (props.getIsToolCall() && isRootRuntime) {
            WorkflowStreamResponse streamResponse =
                    WorkflowStreamResponse.builder()
                            .event(SseResponseEventEnum.INTERACTIVE.getValue())
                            .data(interactiveResult)
                            .build();
            props.getWorkflowStreamResponse().accept(streamResponse);
        }
        AIChatItemValueItemType type = new AIChatItemValueItemType();
        type.setType(ChatItemValueType.INTERACTIVE.getValue());
        type.setInteractive(interactiveResult);
        // 处理交互结果
        return type;
    }

    private List<RuntimeNodeItemType> removeDuplicateNodes(List<RuntimeNodeItemType> nodes) {
        return new ArrayList<>(nodes.stream()
                .collect(Collectors.toMap(
                        RuntimeNodeItemType::getNodeId,
                        node -> node,
                        (existing, replacement) -> existing))
                .values());
    }

    private void sendWorkflowDuration(ChatDispatchProps props, double durationSeconds) {
        // 发送工作流持续时间
        if (props.getWorkflowStreamResponse() != null) {
            WorkflowDurationResponse workflowDurationResponse = WorkflowDurationResponse.builder()
                    .durationSeconds(durationSeconds)
                    .build();

            WorkflowStreamResponse streamResponse =
                    WorkflowStreamResponse.builder()
                            .event(SseResponseEventEnum.WORKFLOW_DURATION.getValue())
                            .data(workflowDurationResponse)
                            .build();
            props.getWorkflowStreamResponse().accept(streamResponse);
        }
    }

    private List<AIChatItemValueItemType> mergeAssistantResponseAnswerText(List<AIChatItemValueItemType> responses) {
        // 合并助手响应文本
        return WorkflowUtil.mergeAssistantResponseAnswerText(responses);
    }

    /**
     * 获取节点运行参数
     */
    private Map<String, Object> getNodeRunParams(RuntimeNodeItemType node, List<RuntimeNodeItemType> runtimeNodes, Map<String, Object> variables) {
        if (FlowNodeTypeEnum.PLUGIN_INPUT.getValue().equals(node.getFlowNodeType())) {
            // 格式化插件输入为对象
            Map<String, Object> params = new HashMap<>();
            if (node.getInputs() != null) {
                node.getInputs().forEach(item -> {
                    params.put(item.getKey(), WorkflowUtil.valueTypeFormat(item.getValue(), item.getValueType()));
                });
            }
            return params;
        }

        // 动态输入需要存储一个键
        FlowNodeInputItemType dynamicInput = null;
        if (node.getInputs() != null) {
            dynamicInput = node.getInputs().stream()
                    .filter(item -> item.getRenderTypeList() != null &&
                            item.getRenderTypeList().contains("addInputParam"))
                    .findFirst().orElse(null);
        }

        Map<String, Object> params = new HashMap<>();
        if (dynamicInput != null) {
            params.put(dynamicInput.getKey(), new HashMap<>());
        }

        if (node.getInputs() != null) {
            FlowNodeInputItemType finalDynamicInput = dynamicInput;
            node.getInputs().forEach(input -> {
                // 特殊输入，不格式化
                if (finalDynamicInput != null && input.getKey().equals(finalDynamicInput.getKey())) {
                    return;
                }

                // 跳过一些特殊键
                if ("childrenNodeIdList".equals(input.getKey()) || "system_httpJsonBody".equals(input.getKey())) {
                    params.put(input.getKey(), input.getValue());
                    return;
                }

                // 替换{{$xx.xx$}}和{{xx}}变量
                Object value = WorkflowUtil.replaceEditorVariable(input.getValue(), runtimeNodes, variables);

                // 替换引用变量
                value = WorkflowUtil.getReferenceVariableValue(value, runtimeNodes, variables);

                // 动态输入存储在动态键中
                if (Boolean.TRUE.equals(input.getCanEdit()) && finalDynamicInput != null &&
                        params.get(finalDynamicInput.getKey()) instanceof Map) {
                    ((Map<String, Object>) params.get(finalDynamicInput.getKey()))
                            .put(input.getKey(), WorkflowUtil.valueTypeFormat(value, input.getValueType()));
                }

                params.put(input.getKey(), WorkflowUtil.valueTypeFormat(value, input.getValueType()));
            });
        }

        return params;
    }

    /**
     * 执行节点逻辑（模拟实现）
     */
    private Map<String, Object> executeNodeLogic(RuntimeNodeItemType node, ModuleDispatchProps dispatchData) {
        // 这里应该根据节点类型调用相应的处理器
        // 目前返回模拟数据
        return nodeService.processNode(node.getFlowNodeType(), dispatchData);
    }

    private void surrenderProcess() {
        // 线程让步
        try {
            Thread.sleep(1);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private String checkNodeRunStatus(RuntimeNodeItemType node, List<RuntimeEdgeItemType> runtimeEdges) {
        // 检查节点运行状态
        return WorkflowUtil.checkNodeRunStatus(node, runtimeEdges);
    }

    private void nodeRunBeforeHook(RuntimeNodeItemType node, List<RuntimeEdgeItemType> runtimeEdges) {
        // 节点运行前钩子
        runtimeEdges.forEach(item -> {
            if (node.getNodeId().equals(item.getTarget())) {
                item.setStatus("waiting");
            }
        });
    }

    private NodeOutputResult nodeOutput(RuntimeNodeItemType node, Map<String, Object> result,
                                        List<RuntimeNodeItemType> runtimeNodes, List<RuntimeEdgeItemType> runtimeEdges,
                                        WorkflowExecutionContext context) {
        // 节点输出处理逻辑
        pushStore(node, result, context);

        // 分配输出值到下一个节点
        node.getOutputs().forEach(outputItem -> {
            if (result.containsKey(outputItem.getKey())) {
                outputItem.setValue(result.get(outputItem.getKey()));
            }
        });

        // 获取下一个源边并更新状态
        List<String> skipHandleId = (List<String>) result.getOrDefault("skipHandleId", new ArrayList<>());
        List<RuntimeEdgeItemType> filterEdges = runtimeEdges.stream()
                .filter(edge -> !"selectedTools".equals(edge.getSourceHandle()) && !"selectedTools".equals(edge.getTargetHandle()))
                .toList();
        List<RuntimeEdgeItemType> targetEdges = filterEdges.stream()
                .filter(item -> node.getNodeId().equals(item.getSource()))
                .toList();

        // 更新边状态
        targetEdges.forEach(edge -> {
            if (skipHandleId.contains(edge.getSourceHandle())) {
                edge.setStatus("skipped");
            } else {
                edge.setStatus("active");
            }
        });

        List<RuntimeNodeItemType> nextStepActiveNodes = new ArrayList<>();
        List<RuntimeNodeItemType> nextStepSkipNodes = new ArrayList<>();

        runtimeNodes.forEach(runtimeNode -> {
            boolean hasActiveEdge = targetEdges.stream()
                    .anyMatch(item -> runtimeNode.getNodeId().equals(item.getTarget()) && "active".equals(item.getStatus()));
            boolean hasSkippedEdge = targetEdges.stream()
                    .anyMatch(item -> runtimeNode.getNodeId().equals(item.getTarget()) && "skipped".equals(item.getStatus()));

            if (hasActiveEdge) {
                nextStepActiveNodes.add(runtimeNode);
            }
            if (hasSkippedEdge) {
                nextStepSkipNodes.add(runtimeNode);
            }
        });

        if ("debug".equals(context.getProps().getMode())) {
            if (context.getProps().getLastInteractive() != null) {
                context.getDebugNextStepRunNodes().addAll(nextStepActiveNodes);
            } else {
                context.getDebugNextStepRunNodes().addAll(nextStepActiveNodes);
                context.getDebugNextStepRunNodes().addAll(nextStepSkipNodes);
            }
            return NodeOutputResult.builder()
                    .nextStepActiveNodes(new ArrayList<>())
                    .nextStepSkipNodes(new ArrayList<>())
                    .build();
        }

        return NodeOutputResult.builder()
                .nextStepActiveNodes(nextStepActiveNodes)
                .nextStepSkipNodes(nextStepSkipNodes)
                .build();
    }

    /**
     * 存储特殊响应字段
     */
    private void pushStore(RuntimeNodeItemType node, Map<String, Object> result, WorkflowExecutionContext context) {
        // 添加运行次数
        Integer runTimes = (Integer) result.getOrDefault("runTimes", 1);
        context.setWorkflowRunTimes(context.getWorkflowRunTimes() + runTimes);
        context.getProps().setMaxRunTimes(context.getProps().getMaxRunTimes() - runTimes);

        // 更新系统内存
        Map<String, Object> newMemories = (Map<String, Object>) result.get("systemMemories");
        if (newMemories != null) {
            context.getSystemMemories().putAll(newMemories);
        }

        // 添加响应数据
        Map<String, Object> responseData = (Map<String, Object>)result.get("responseData");
        if (responseData != null && !responseData.isEmpty()) {
            ChatHistoryItemResType chatHistoryItemResType = objectMapper.convertValue(responseData, ChatHistoryItemResType.class);
            // TODO 类型转换异常
            List<ChatHistoryItemResType> chatResponses = context.getChatResponses();
            chatResponses.add(chatHistoryItemResType);
        }

        // 添加节点使用情况
        List<ChatNodeUsageType> nodeDispatchUsages = (List<ChatNodeUsageType>) result.get("nodeDispatchUsages");
        if (nodeDispatchUsages != null) {
            context.getChatNodeUsages().addAll(nodeDispatchUsages);
        }

        // 处理工具响应
        Object toolResponses = result.get("toolResponses");
        if (toolResponses != null) {
            if (toolResponses instanceof List && ((List<?>) toolResponses).isEmpty()) {
                return;
            }
            if (toolResponses instanceof Map && ((Map<?, ?>) toolResponses).isEmpty()) {
                return;
            }
            context.setToolRunResponse(toolResponses);
        }

        // 历史记录存储
        List<AIChatItemValueItemType> assistantResponses = (List<AIChatItemValueItemType>) result.get("assistantResponses");
        if (assistantResponses != null) {
            context.getChatAssistantResponse().addAll(assistantResponses);
        } else {
            handleTextResponses(node, result, context);
        }

        // 重写历史记录
        List<ChatItemType> rewriteHistories = (List<ChatItemType>) result.get("rewriteHistories");
        if (rewriteHistories != null) {
            context.setHistories(rewriteHistories);
        }
    }

    /**
     * 处理文本响应
     */
    private void handleTextResponses(RuntimeNodeItemType node, Map<String, Object> result, WorkflowExecutionContext context) {
        String reasoningText = (String) result.get("reasoningText");
        String answerText = (String) result.get("answerText");

        if (reasoningText != null) {
            boolean isResponseReasoningText = node.getInputs().stream()
                    .anyMatch(item -> "aiChatReasoning".equals(item.getKey()) && Boolean.TRUE.equals(item.getValue()));
            if (isResponseReasoningText) {
                AIChatItemValueItemType reasoningItem = new AIChatItemValueItemType();
                reasoningItem.setType(ChatItemValueType.TEXT.getValue());
                reasoningItem.setReasoning(ReasoningContent.builder().content(reasoningText).build());
                context.getChatAssistantResponse().add(reasoningItem);
            }
        }

        if (answerText != null) {
            boolean isResponseAnswerText = node.getInputs().stream()
                    .filter(item -> "isResponseAnswerText".equals(item.getKey()))
                    .findFirst()
                    .map(item -> (Boolean) Optional.ofNullable(item.getValue()).orElse(true))
                    .orElse(true);
            if (isResponseAnswerText) {
                AIChatItemValueItemType textItem = new AIChatItemValueItemType();
                textItem.setType(ChatItemValueType.TEXT.getValue());
                textItem.setText(TextContent.builder().content(answerText).build());
                context.getChatAssistantResponse().add(textItem);
            }
        }
    }

    private String getSystemTime(String timezone) {
        // 实现系统时间获取逻辑
        return Instant.now().toString();
    }

    /**
     * 设置SSE响应头
     * @param res SSE发射器
     */
    private void setupSseHeaders(SseEmitter res) {
        // 设置SSE响应头
        try {
            res.send(SseEmitter.event().name("connected").data("Connection established"));
        } catch (Exception e) {
            log.error("设置SSE响应头失败", e);
        }
    }

    private void setupStreamResponse(SseEmitter res, ChatDispatchProps data) {
        // 设置流式响应
        // 10秒发送一次心跳消息，防止浏览器认为连接断开
        // 不能使用守护线程的方法进行运行，否则无法停下
        Timer timer = new Timer();
        timer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                try {
                    if (data.getWorkflowStreamResponse() != null) {
                        WorkflowStreamResponse heartbeat =
                                WorkflowStreamResponse.builder()
                                        .event(SseResponseEventEnum.ANSWER.getValue())
                                        .data(WorkflowUtil.textAdaptGptResponse(TextAdaptGptResponseParams.builder().text("").build()))
                                        .build();
                        data.getWorkflowStreamResponse().accept(heartbeat);
                    }
                } catch (Exception e) {
                    log.error("发送心跳消息失败", e);
                    timer.cancel();
                }
            }
        }, 10000, 10000);

        // 当连接关闭时取消定时器
        res.onCompletion(timer::cancel);
        res.onTimeout(timer::cancel);
        res.onError(throwable -> timer.cancel());
    }
}