package com.sinitek.mind.core.chat.service.impl;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.dto.AuthAppDTO;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.VariableItemType;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.app.util.PluginUtils;
import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.dto.*;
import com.sinitek.mind.core.chat.entity.Chat;
import com.sinitek.mind.core.chat.entity.ChatItem;
import com.sinitek.mind.core.chat.enumerate.ChatItemValueType;
import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;
import com.sinitek.mind.core.chat.enumerate.GetChatTypeEnum;
import com.sinitek.mind.core.chat.model.*;
import com.sinitek.mind.core.chat.repository.ChatItemRepository;
import com.sinitek.mind.core.chat.repository.ChatRepository;
import com.sinitek.mind.core.chat.service.IAuthChatService;
import com.sinitek.mind.core.chat.service.IChatService;
import com.sinitek.mind.core.chat.util.ChatUtil;
import com.sinitek.mind.core.dataset.model.SearchDataResponseItemType;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.model.sse.WorkflowResponseWriter;
import com.sinitek.mind.core.workflow.service.IWorkflowService;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.support.outlink.service.IAuthOutLinkService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.team.dto.OpenaiAccountType;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChatServiceImpl implements IChatService {

    private final ChatRepository chatRepository;
    private final IPermissionService permissionService;
    private final IAuthService authService;
    private final AppRepository appRepository;
    private final ChatItemRepository chatItemRepository;
    private final IWorkflowService workflowService;
    private final MongoTemplate mongoTemplate;
    private final IAuthOutLinkService authOutLinkService;
    private final IAuthAppService authAppService;
    private final IAuthChatService authChatService;

    @Override
    public InitChatResponse initChat(InitChatRequest request) {
        String appId = request.getAppId();
        String chatId = request.getChatId();

        try {
            // 获取当前用户认证信息
            AuthDTO authDTO = authService.authCert();

            // 验证应用权限
            AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.READ_PER);

            App app = authAppDTO.getApp();
            String tmbId = authAppDTO.getTmbId();

            if (app == null) {
                throw new BussinessException("应用不存在或无权限访问");
            }

            // 查找聊天记录（如果提供了chatId）
            Chat chat = null;
            if (StringUtils.hasText(chatId)) {
                Optional<Chat> chatOpt = chatRepository.findByAppIdAndChatId(appId, chatId);
                if (chatOpt.isPresent()) {
                    chat = chatOpt.get();
                    // 验证聊天权限
                    if (!ChatUtil.validateChatPermission(app, chat, tmbId)) {
                        throw new BussinessException("无权限访问此聊天");
                    }
                }
            }

            // 构建响应
            InitChatResponse response = new InitChatResponse();
            response.setChatId(chatId);
            response.setAppId(appId);
            response.setTitle(chat != null ? chat.getTitle() : null);
            response.setUserAvatar(null); // 根据需要设置用户头像
            response.setVariables(chat != null ? chat.getVariables() : null);

            // 构建应用信息
            InitChatResponse.AppInfo appInfo = new InitChatResponse.AppInfo();
            appInfo.setChatConfig(ChatUtil.getAppChatConfig(app, chat));
            appInfo.setChatModels(ChatUtil.getChatModelNameListByModules(app.getModules()));
            appInfo.setName(app.getName());
            appInfo.setAvatar(app.getAvatar());
            appInfo.setIntro(app.getIntro());
            appInfo.setCanUse(true); // 根据实际业务逻辑设置
            appInfo.setType(app.getType());
            appInfo.setPluginInputs(ChatUtil.getPluginInputs(app, chat));

            response.setApp(appInfo);

            return response;

        } catch (Exception e) {
            log.error("初始化聊天失败: appId={}, chatId={}, error={}", appId, chatId, e.getMessage(), e);
            throw new BussinessException("初始化聊天失败: " + e.getMessage());
        }
    }

    @Override
    public PageResult<ChatItem> getPaginationRecords(GetPaginationRecordsRequest request) {

        // 获取应用信息
        Optional<App> appOpt = appRepository.findById(request.getAppId());
        if (appOpt.isEmpty()) {
            throw new BussinessException("没有对应App");
        }
        App app = appOpt.get();

        // TODO 根据聊天类型进行不同的权限验证 authChatCrud

        // 如果存在shareId，需要进行权限校验
        if (StringUtils.hasText(request.getShareId())) {
            String shareId = request.getShareId();
            // 身份验证
            authOutLinkService.authOutLinkChatInit(shareId, request.getAuthToken());
        }

        // 验证聊天记录是否存在
        boolean exists = chatRepository.findByAppIdAndChatId(request.getAppId(), request.getChatId()).isPresent();
        if (!exists) {
            return PageResult.of(0, List.of());
        }
        // 构建分页参数
        Pageable pageable = PageRequest.of(
                request.getOffset() / request.getPageSize(),
                request.getPageSize(),
                Sort.by(Sort.Direction.ASC, "time")
        );

        // 分页查询聊天记录
        Page<ChatItem> chatItemPage = chatItemRepository.findByChatIdAndAppIdOrderByTimeDesc(
                request.getChatId(),
                request.getAppId(),
                pageable
        );

        // 处理聊天记录数据
        List<ChatItem> processedItems = processChatItems(
                chatItemPage.getContent(),
                app,
                request.getType(),
                request.getLoadCustomFeedbacks()
        );

        // 构建响应
        PageResult<ChatItem> response = new PageResult<>();
        response.setTotal((int) chatItemPage.getTotalElements());
        response.setList(processedItems);

        return response;
    }

    @Override
    public void processChatTest(ChatTestRequest request, SseEmitter emitter) {
        try {
            // 参数验证
            validateChatTestRequest(request);

            List<ChatItemType> chatMessages = ChatAdaptor.gptMessages2Chats(request.getMessages(), true);

            // 验证应用权限
            AuthAppDTO authAppDTO = authAppService.authApp(request.getAppId(), PermissionConstant.READ_PER);

            App app = authAppDTO.getApp();
            String tmbId = authAppDTO.getTmbId();
            String teamId = authAppDTO.getTeamId();

            if (app == null) {
                throw new BussinessException("应用不存在或无权限访问");
            }

            boolean isPlugin = AppTypeEnum.PLUGIN.getValue().equals(app.getType());
            boolean isTool = AppTypeEnum.TOOL.getValue().equals(app.getType());

            // 获取或创建聊天记录
            Chat chat = getOrCreateChat(request, app, tmbId, teamId);

            // 处理用户问题
            ChatItemType userQuestion = extractUserQuestion(chatMessages, app, isPlugin, isTool, request.getVariables());

            // 获取数量限制
            int limit = WorkflowUtil.getMaxHistoryLimitFromNodes(request.getNodes());

            // 获取聊天历史
            List<ChatItemType> histories = getChatHistories(request.getAppId(), request.getChatId(), 0, limit, "dataId obj value memories");

            // 合并变量
            Map<String, Object> variables = mergeVariables(request.getVariables(), chat != null ? chat.getVariables() : null);
            request.setVariables(variables);

            List<ChatItemType> newHistories = ChatUtil.concatHistories(histories, chatMessages);

            WorkflowInteractiveResponseType interactive = WorkflowUtil.getLastInteractiveValue(newHistories);

            List<String> nodeIds = WorkflowUtil.getWorkflowEntryNodeIds(request.getNodes(), interactive);
            // Get runtimeNodes
            List<RuntimeNodeItemType> runtimeNodes = WorkflowUtil.storeNodes2RuntimeNodes(request.getNodes(), nodeIds);
            List<RuntimeEdgeItemType> runtimeEdges = WorkflowUtil.storeEdges2RuntimeEdges(request.getEdges(), interactive);
            // 如果是插件根据变量更新插件输入
            if (isPlugin) {
                runtimeNodes = WorkflowUtil.updatePluginInputByVariables(runtimeNodes, variables);
                variables = new HashMap<>();
            }

            // 使用历史交互数据重写节点输出
            runtimeNodes = WorkflowUtil.rewriteNodeOutputByHistories(runtimeNodes, interactive);

            WorkflowResponseConfig config = WorkflowResponseConfig.builder()
                    .sseEmitter(emitter)
                    .detail(true)
                    .streamResponse(true)
                    .id(request.getChatId())
                    .showNodeStatus(true)
                    .build();
            // TODO getUserChatInfoAndAuthTeamPoints方法实现
            ExternalProviderType externalProviderType = new ExternalProviderType();
            externalProviderType.setExternalWorkflowVariables(new HashMap<>());
            externalProviderType.setOpenaiAccount(new OpenaiAccountType());

            // 构建工作流调度参数
            ChatDispatchProps dispatchProps = ChatDispatchProps.builder()
                    .res(emitter)
                    .mode("test")
                    .timezone("Asia/Shanghai")
                    .externalProvider(externalProviderType)
                    .uid(tmbId)
                    .runningAppInfo(RunningAppInfo.builder()
                            .id(app.getId())
                            .teamId(teamId)
                            .tmbId(tmbId)
                            .build())
                    .runningUserInfo(RunningUserInfo.builder()
                            .teamId(teamId)
                            .tmbId(tmbId)
                            .build())
                    .chatId(request.getChatId())
                    .responseChatItemId(request.getResponseChatItemId())
                    .runtimeNodes(runtimeNodes)
                    .runtimeEdges(runtimeEdges)
                    .variables(variables)
                    .query(ChatUtil.removeEmptyUserInput(userQuestion.getValue()))
                    .lastInteractive(interactive)
                    .chatConfig(request.getChatConfig())
                    .histories(newHistories)
                    .stream(true)
                    .maxRunTimes(500)
                    .workflowStreamResponse(WorkflowUtil.getWorkflowResponseWrite(config))
                    .version("v2")
                    .responseDetail(true)
                    .build();

            // 执行工作流并获取结果
            DispatchFlowResponse dispatchResult;
            try {
                dispatchResult = workflowService.dispatchWorkFlow(dispatchProps).get();
            } catch (ExecutionException | InterruptedException e) {
                log.error("工作流执行失败: {}", e.getMessage(), e);
                throw new RuntimeException("工作流执行失败: " + e.getMessage(), e);
            }

            // 发送工作流结束信号
            WorkflowResponseWriter.sseComplete(emitter);


            // 转换为ChatTestResponse格式
//            ChatTestResponse workflowResult = convertDispatchResultToChatTestResponse(dispatchResult);

            // 保存聊天记录
            boolean isInteractiveRequest = WorkflowUtil.getLastInteractiveValue(histories) != null;
            String userInteractiveVal = ChatAdaptor.chatValue2RuntimePrompt(userQuestion.getValue()).getText();

            String newTitle = isPlugin
                    ? (variables.containsKey("cTime") ? variables.get("cTime").toString() : getCurrentTime())
                    : ChatUtil.getChatTitleFromChatMessage(userQuestion);

            ChatItemType aiResponse = createAiResponse(request.getResponseChatItemId(), dispatchResult);

            if (isInteractiveRequest) {
                updateInteractiveChat(request.getChatId(), request.getAppId(), userInteractiveVal,
                        aiResponse, dispatchResult.getNewVariables(), dispatchResult.getDurationSeconds());
            } else {
                SaveChatDTO dto = SaveChatDTO.builder()
                        .chatId(request.getChatId())
                        .appId(request.getAppId())
                        .teamId(teamId)
                        .tmbId(tmbId)
                        .nodes(request.getNodes())
                        .appChatConfig(request.getChatConfig())
                        .variables(dispatchResult.getNewVariables())
                        .isUpdateUseTime(false)
                        .newTitle(newTitle)
                        .source("test")
                        .content(List.of(userQuestion, aiResponse))
                        .durationSeconds(dispatchResult.getDurationSeconds())
                        .build();
                saveChat(dto);
            }

            // 创建使用统计
            createChatUsage(request.getAppName(), request.getAppId(), teamId, tmbId,
                    "fastgpt", dispatchResult.getFlowUsage());

        } catch (Exception e) {
            log.error("聊天测试失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public PageResult<Chat> getHistories(GetHistoriesRequest request) {
        log.info("获取聊天历史记录，请求参数：{}", request);

        try {
            Query query = new Query();
            String outLinkUid = request.getOutLinkUid();

            String appId = request.getAppId();
            String teamId = request.getTeamId();
            String teamToken = request.getTeamToken();
            String source = request.getSource();

            // 当前只支持外链认证方式
            if (StringUtils.hasText(request.getShareId()) && StringUtils.hasText(request.getOutLinkUid())) {
                // 进行外链身份验证
                authOutLinkService.authOutLinkChatInit(request.getShareId(), request.getAuthToken());
                query.addCriteria(Criteria.where("outLinkUid").is(outLinkUid));
                query.addCriteria(Criteria.where("shareId").is(request.getShareId()));
                query.addCriteria(Criteria.where("updateTime").gte(new Date(System.currentTimeMillis() - 30L * 24 * 60 * 60 * 1000)));
            } else if (StringUtils.hasText(appId) && StringUtils.hasText(teamId) && StringUtils.hasText(teamToken)) {
                // TODO 需要进行验证teamId和teamToken，但是目前没有对应概念，不实现
                throw new BussinessException("无法使用teamId和teamToken，获取聊天记录");
            } else if (StringUtils.hasText(appId)) {
                AuthDTO authDTO = authService.authCert();
                String tmbId = authDTO.getTmbId();
                query.addCriteria(Criteria.where("tmbId").is(tmbId));
                query.addCriteria(Criteria.where("appId").is(appId));
                if (StringUtils.hasText(source)) {
                    query.addCriteria(Criteria.where("source").is(source));
                }
            } else {
                return PageResult.of(0, List.of());
            }

            Date startCreateTime = request.getStartCreateTime();
            Date endCreateTime = request.getEndCreateTime();
            Date startUpdateTime = request.getStartUpdateTime();
            Date endUpdateTime = request.getEndUpdateTime();

            if (!Objects.isNull(startCreateTime) || !Objects.isNull(endCreateTime)) {
                query.addCriteria(Criteria.where("createTime").gte(startCreateTime).lte(endCreateTime));
            }

            if (!Objects.isNull(startUpdateTime) || !Objects.isNull(endUpdateTime)) {
                query.addCriteria(Criteria.where("createTime").gte(startUpdateTime).lte(endUpdateTime));
            }
            // 构建分页参数和排序
            query.with(Sort.by(
                    Sort.Order.desc("top"),
                    Sort.Order.desc("updateTime")
            ));

            // 添加分页参数前，查询总数
            long count = mongoTemplate.count(query, Chat.class);

            query.skip(request.getOffset());
            query.limit(request.getPageSize());

            // 分页查询聊天历史记录
            List<Chat> chats = mongoTemplate.find(query, Chat.class);

            // 构建响应
            return PageResult.of((int) count, chats);

        } catch (Exception e) {
            log.error("获取聊天历史记录失败", e);
            if (e instanceof BussinessException) {
                throw e;
            }
            throw new BussinessException("获取聊天历史记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取或创建聊天记录
     */
    private Chat getOrCreateChat(ChatTestRequest request, App app, String tmbId, String teamId) {
        String chatId = request.getChatId();

        if (StringUtils.hasText(chatId)) {
            Optional<Chat> chatOpt = chatRepository.findByAppIdAndChatId(request.getAppId(), chatId);
            if (chatOpt.isPresent()) {
                Chat chat = chatOpt.get();
                if (!ChatUtil.validateChatPermission(app, chat, tmbId)) {
                    throw new BussinessException("无权限访问此聊天");
                }
                return chat;
            }
        }

        // 创建新的聊天记录
        Chat newChat = new Chat();
        newChat.setAppId(request.getAppId());
        newChat.setChatId(chatId != null ? chatId : generateChatId());
        newChat.setTmbId(tmbId);
        newChat.setTeamId(teamId);
        newChat.setTitle("测试聊天");
        newChat.setVariables(request.getVariables());

        return chatRepository.save(newChat);
    }

    /**
     * 验证聊天测试请求参数
     */
    private void validateChatTestRequest(ChatTestRequest request) {
        if (request == null) {
            throw new BussinessException("请求参数不能为空");
        }
        if (!StringUtils.hasText(request.getAppId())) {
            throw new BussinessException("应用ID不能为空");
        }
        if (request.getNodes() == null || request.getNodes().isEmpty()) {
            throw new BussinessException("节点列表不能为空");
        }
        if (request.getEdges() == null) {
            throw new BussinessException("边列表不能为空");
        }
        if (request.getMessages() == null) {
            throw new BussinessException("消息列表不能为空");
        }
    }

    /**
     * 提取用户问题
     */
    private ChatItemType extractUserQuestion(List<ChatItemType> chatMessages, App app,
                                             boolean isPlugin, boolean isTool, Map<String, Object> variables) {
        if (isPlugin) {

            List<FlowNodeInputItemType> pluginInputs = PluginUtils.getPluginInputsFromStoreNodes(app.getModules());
            List<ChatItemValueItemFileInfo> files = (List<ChatItemValueItemFileInfo>)variables.get("files");

            // 使用WorkflowUtil方法获取查询文本
            return WorkflowUtil.getPluginRunUserQuery(pluginInputs, variables, files);
        }

        if (isTool) {
            // 工具类型应用的处理逻辑
            ChatItemType toolQuestion = new ChatItemType();
            toolQuestion.setObj(ChatRoleEnum.HUMAN);
            List<ChatItemValueItemType> value = new ArrayList<>();
            ChatItemValueItemType itemType = new ChatItemValueItemType();
            itemType.setType(ChatItemValueType.TEXT.getValue());
            ChatItemValueItemTextInfo textContent = new ChatItemValueItemTextInfo();
            textContent.setContent("tool");
            itemType.setText(textContent);
            value.add(itemType);
            toolQuestion.setValue(value);
            return toolQuestion;
        }

        // 普通聊天应用，从消息列表中获取最后一条用户消息
        if (chatMessages != null && !chatMessages.isEmpty()) {
            for (int i = chatMessages.size() - 1; i >= 0; i--) {
                ChatItemType message = chatMessages.get(i);
                if ("Human".equals(message.getObj().getValue())) {
                    return message;
                }
            }
        }

        throw new BussinessException("用户问题为空");
    }

    /**
     * 合并变量
     */
    private Map<String, Object> mergeVariables(Map<String, Object> requestVariables, Map<String, Object> chatVariables) {
        Map<String, Object> result = new HashMap<>();
        if (chatVariables != null) {
            result.putAll(chatVariables);
        }
        if (requestVariables != null) {
            result.putAll(requestVariables);
        }
        return result;
    }

    /**
     * 获取聊天历史
     */
    private List<ChatItemType> getChatHistories(String appId, String chatId, Integer offset, Integer limit, String field) {
        if (!StringUtils.hasText(chatId)) {
            return new ArrayList<>();
        }
        Query query = new Query(Criteria.where("chatId").is(chatId)
                .and("appId").is(appId));
        if (field != null) {
            String[] fields = field.split(" ");
            for (String item : fields) {
                query.fields().include(item.trim());
            }
        }
        // 设置排序、跳过和限制
        query.with(Sort.by(Sort.Direction.DESC, "_id"))
                .skip(offset)
                .limit(limit);
        List<ChatItemType> histories = mongoTemplate.find(query, ChatItemType.class, "chatItems");
        // 反转列表以获得正确的时间顺序
        Collections.reverse(histories);
        return histories;
    }

    /**
     * 获取当前时间
     */
    private String getCurrentTime() {
        return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date());
    }

    /**
     * 创建AI响应
     */
    private ChatItemType createAiResponse(String responseChatItemId, DispatchFlowResponse workflowResult) {
        try {
            List<ChatItemValueItemType> newType = new ArrayList<>();
            for (AIChatItemValueItemType type : workflowResult.getAssistantResponses()) {
                ChatItemValueItemType itemValueItemType = new ChatItemValueItemType();
                BeanUtils.copyProperties(type, itemValueItemType);
                newType.add(itemValueItemType);
            }
            ChatItemType aiResponse = new ChatItemType();
            aiResponse.setDataId(responseChatItemId);
            aiResponse.setObj(ChatRoleEnum.AI);
            aiResponse.setValue(newType);
            aiResponse.setMemories(workflowResult.getSystem_memories());
            aiResponse.setResponseData(workflowResult.getFlowResponses());
            return aiResponse;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 更新交互式聊天
     */
    public void updateInteractiveChat(String chatId, String appId, String userInteractiveVal,
                                       ChatItemType aiResponse, Map<String, Object> newVariables,
                                       Double durationSeconds) {
        // TODO 逻辑需要重新写
        try {
            // 查找现有的聊天记录
            Optional<Chat> chatOpt = chatRepository.findByAppIdAndChatId(appId, chatId);
            if (chatOpt.isPresent()) {
                Chat chat = chatOpt.get();
                chat.setVariables(newVariables);
                chat.setUpdateTime(new java.util.Date());
                chatRepository.save(chat);

                // 更新最后的聊天项
                // 这里可以添加更新聊天项的逻辑
                log.info("更新交互式聊天: chatId={}, userVal={}", chatId, userInteractiveVal);
            }
        } catch (Exception e) {
            log.error("更新交互式聊天失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 保存聊天
     */
    @Override
    public void saveChat(SaveChatDTO dto) {

        String chatId = dto.getChatId();
        if (chatId == null || "NO_RECORD_HISTORIES".equals(chatId)) {
            return;
        }

        try {
            // 查找现有聊天记录
            Chat chat = chatRepository.findByAppIdAndChatId(dto.getAppId(), dto.getChatId()).orElse(null);
            Map<String, Object> metadataUpdate = chat == null ? new HashMap<>() : chat.getMetadata();

            if (dto.getMetadata() != null) {
                metadataUpdate.putAll(dto.getMetadata());
            }

            // 获取应用聊天配置
            GetAppChatConfigReq req = GetAppChatConfigReq.builder()
                    .chatConfig(dto.getAppChatConfig())
                    .systemConfigNode(WorkflowUtil.getGuideModule(dto.getNodes()))
                    .isPublicFetch(false)
                    .build();

            AppChatConfigType appChatConfig = WorkflowUtil.getAppChatConfig(req);

            String welcomeText = appChatConfig.getWelcomeText();
            List<VariableItemType> variableList = appChatConfig.getVariables();

            // 获取插件输入
            List<FlowNodeInputItemType> pluginInputs = dto.getNodes().stream()
                    .filter(node -> FlowNodeTypeEnum.PLUGIN_INPUT.getValue().equals(node.getFlowNodeType()))
                    .findFirst()
                    .map(StoreNodeItemType::getInputs)
                    .orElse(null);

            // 处理聊天内容：移除引用问答
            List<ChatItemType> processedContent = dto.getContent().stream().map(item -> {
                if (ChatRoleEnum.AI.equals(item.getObj())) {
                    // 处理AI响应的nodeResponse
                    List<ChatHistoryItemResType> nodeResponse = item.getResponseData();
                    if (nodeResponse != null) {
                        List<ChatHistoryItemResType> processedNodeResponse = nodeResponse.stream().map(responseItem -> {
                            if (FlowNodeTypeEnum.DATASET_SEARCH_NODE.getValue().equals(responseItem.getModuleType()) &&
                                    responseItem.getQuoteList() != null) {
                                // 处理引用列表，只保留必要字段
                                List<SearchDataResponseItemType> quoteList = responseItem.getQuoteList();
                                List<SearchDataResponseItemType> processedQuoteList = quoteList.stream()
                                        .map(quote -> {
                                            SearchDataResponseItemType processedQuote = new SearchDataResponseItemType();
                                            processedQuote.setId(quote.getId());
                                            processedQuote.setChunkIndex(quote.getChunkIndex());
                                            processedQuote.setDatasetId(quote.getDatasetId());
                                            processedQuote.setCollectionId(quote.getCollectionId());
                                            processedQuote.setSourceId(quote.getSourceId());
                                            processedQuote.setSourceName(quote.getSourceName());
                                            processedQuote.setScore(quote.getScore());
                                            // todo processedQuote.setTokens(quote.getTokens());
                                            return processedQuote;
                                        })
                                        .collect(Collectors.toList());

                                // 创建新的响应项
                                ChatHistoryItemResType newResponseItem = new ChatHistoryItemResType();
                                try {
                                    BeanUtils.copyProperties(responseItem, newResponseItem);
                                } catch (Exception e) {
                                    log.error("复制ChatHistoryItemResType属性失败: {}", e.getMessage(), e);
                                    newResponseItem = responseItem;
                                }
                                newResponseItem.setQuoteList(processedQuoteList);
                                return newResponseItem;
                            }
                            return responseItem;
                        }).collect(Collectors.toList());

                        // 创建新的ChatItemType对象
                        ChatItemType newItem = new ChatItemType();
                        newItem.setValue(item.getValue());
                        newItem.setMemories(item.getMemories());
                        newItem.setDataId(item.getDataId());
                        newItem.setHideInUI(item.getHideInUI());
                        newItem.setObj(item.getObj());
                        newItem.setAdminFeedback(item.getAdminFeedback());
                        newItem.setCustomFeedbacks(item.getCustomFeedbacks());
                        newItem.setLlmModuleAccount(item.getLlmModuleAccount());
                        newItem.setTotalQuoteList(item.getTotalQuoteList());
                        newItem.setHistoryPreviewLength(item.getHistoryPreviewLength());
                        newItem.setUserBadFeedback(item.getUserBadFeedback());
                        newItem.setUserGoodFeedback(item.getUserGoodFeedback());
                        newItem.setResponseData(processedNodeResponse);
                        return newItem;
                    }
                }
                return item;
            }).toList();

            // 使用事务保存聊天项和聊天记录
            List<ChatItem> savedChatItems = new ArrayList<>();

            // 保存聊天项
            for (ChatItemType contentItem : processedContent) {
                ChatItem chatItem = new ChatItem();
                chatItem.setChatId(dto.getChatId());
                chatItem.setTeamId(dto.getTeamId());
                chatItem.setTmbId(dto.getTmbId());
                chatItem.setAppId(dto.getAppId());
                chatItem.setDataId(contentItem.getDataId());
                chatItem.setObj(contentItem.getObj() != null ? contentItem.getObj().getValue() : null);
                chatItem.setValue(contentItem.getValue());
                chatItem.setMemories(contentItem.getMemories());
                // 设置响应数据，需要转换为合适的格式
                if (contentItem.getResponseData() != null) {
                    chatItem.setResponseData(Collections.singletonList(contentItem.getResponseData()));
                }
                chatItem.setDurationSeconds(dto.getDurationSeconds());
                chatItem.setErrorMsg(dto.getErrorMsg());
                chatItem.setTime(new Date());

                ChatItem saved = chatItemRepository.save(chatItem);
                savedChatItems.add(saved);
            }

            // 更新或创建聊天记录
            if (chat == null) {
                chat = new Chat();
                chat.setChatId(dto.getChatId());
                chat.setAppId(dto.getAppId());
                chat.setCreateTime(new Date());
            }

            chat.setTeamId(dto.getTeamId());
            chat.setTmbId(dto.getTmbId());
            chat.setVariableList(variableList);
            chat.setWelcomeText(welcomeText);
            chat.setVariables(dto.getVariables() != null ? dto.getVariables() : new HashMap<>());
            chat.setPluginInputs(pluginInputs);
            chat.setTitle(dto.getNewTitle());
            chat.setSource(dto.getSource());
            chat.setSourceName(dto.getSourceName());
            chat.setShareId(dto.getShareId());
            chat.setOutLinkUid(dto.getOutLinkUid());
            chat.setMetadata(metadataUpdate);
            chat.setUpdateTime(new Date());

            chatRepository.save(chat);

            // 推送聊天日志（如果有人工和AI响应）
            if (savedChatItems.size() >= 2) {
                String chatItemIdHuman = null;
                String chatItemIdAi = null;

                for (ChatItem item : savedChatItems) {
                    if (ChatRoleEnum.HUMAN.getValue().equals(item.getObj())) {
                        chatItemIdHuman = item.getId();
                    } else if (ChatRoleEnum.AI.getValue().equals(item.getObj())) {
                        chatItemIdAi = item.getId();
                    }
                }

                if (chatItemIdHuman != null && chatItemIdAi != null) {
                    pushChatLog(dto.getChatId(), chatItemIdHuman, chatItemIdAi, dto.getAppId());
                }
            }

            // 如果需要更新使用时间，更新应用的updateTime
            if (dto.isUpdateUseTime()) {
                Optional<App> appOpt = appRepository.findById(dto.getAppId());
                if (appOpt.isPresent()) {
                    App app = appOpt.get();
                    app.setUpdateTime(new Date());
                    appRepository.save(app);
                }
            }
            log.info("保存聊天成功: chatId={}, title={}", dto.getChatId(), dto.getNewTitle());
        } catch (Exception e) {
            log.error("保存聊天失败: chatId={}, error={}", dto.getChatId(), e.getMessage(), e);
        }
    }

    /**
     * 推送聊天日志 packages\service\core\chat\pushChatLog.ts
     */
    private void pushChatLog(String chatId, String chatItemIdHuman, String chatItemIdAi, String appId) {
        try {
            // TODO: 实现聊天日志推送逻辑
            log.info("推送聊天日志: chatId={}, humanId={}, aiId={}, appId={}",
                    chatId, chatItemIdHuman, chatItemIdAi, appId);
        } catch (Exception e) {
            log.error("推送聊天日志失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建聊天使用统计
     */
    @Override
    public int createChatUsage(String appName, String appId, String teamId, String tmbId,
                                 String source, List<ChatNodeUsageType> flowUsages) {
        try {
            // TODO 这里可以添加使用统计的逻辑
            log.info("创建聊天使用统计: appId={}, source={}", appId, source);
        } catch (Exception e) {
            log.error("创建聊天使用统计失败: {}", e.getMessage(), e);
        }
        return 1;
    }

    /**
     * 生成聊天ID
     */
    private String generateChatId() {
        return "chat_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    /**
     * 处理聊天记录数据
     */
    private List<ChatItem> processChatItems(List<ChatItem> items, App app, String chatType, Boolean loadCustomFeedbacks) {
        if (items == null || items.isEmpty()) {
            return items;
        }

        boolean isPlugin = AppTypeEnum.PLUGIN.getValue().equals(app.getType());
        boolean isOutLink = chatType.equals(GetChatTypeEnum.OUT_LINK.getValue());

        // 根据不同的聊天类型和应用类型处理数据
        for (ChatItem item : items) {
            // 如果是外链聊天且不是插件应用，需要过滤敏感信息
            if (isOutLink && !isPlugin) {
                filterSensitiveData(item);
            }

            // 如果不需要加载自定义反馈，清除相关字段
            if (!Boolean.TRUE.equals(loadCustomFeedbacks)) {
                item.setCustomFeedback(null);
            }
        }

        return items;
    }

    /**
     * 过滤敏感数据
     */
    private void filterSensitiveData(ChatItem item) {
        // TODO 这里可以根据需要过滤敏感信息
        // 例如：移除某些响应数据、隐藏错误信息等
        if (ChatRoleEnum.AI.getValue().equals(item.getObj())) {
            // 可以在这里添加具体的过滤逻辑
            // 例如：过滤responseData中的敏感信息
        }
    }

    @Override
    public void addCustomFeedbacks(String appId, String chatId, String dataId, List<String> feedbacks) {
        if (chatId == null || chatId.isEmpty() || dataId == null || dataId.isEmpty()) {
            return;
        }

        try {
            Query query = new Query(Criteria.where("appId").is(appId)
                    .and("chatId").is(chatId)
                    .and("dataId").is(dataId));

            org.springframework.data.mongodb.core.query.Update update = 
                new org.springframework.data.mongodb.core.query.Update()
                    .push("customFeedback").each(feedbacks.toArray());

            mongoTemplate.findAndModify(query, update, ChatItem.class);

            log.info("成功添加自定义反馈: appId={}, chatId={}, dataId={}, feedbacks={}", 
                    appId, chatId, dataId, feedbacks);

        } catch (Exception e) {
            log.error("添加自定义反馈失败: appId={}, chatId={}, dataId={}, error={}", 
                    appId, chatId, dataId, e.getMessage(), e);
        }
    }

    @Override
    public void updateHistory(UpdateHistoryRequest request) {
        Boolean top = request.getTop();
        String title = request.getTitle();
        String customTitle = request.getCustomTitle();
        String appId = request.getAppId();
        String chatId = request.getChatId();

        authChatService.authChatCrud(AuthChatCrudParams.builder()
                .appId(appId)
                .chatId(chatId)
                .per(PermissionConstant.WRITE_PER)
                .build());

        Query query = new Query()
                .addCriteria(Criteria.where("appId").is(appId))
                .addCriteria(Criteria.where("chatId").is(chatId));

        Update update = new Update()
                .set("updateTime", new Date());

        // 只有非 null 时才更新对应字段
        if (title != null) {
            update.set("title", title);
        }
        if (customTitle != null) {
            update.set("customTitle", customTitle);
        }
        if (top != null) {
            update.set("top", top);
        }

        update.set("updateTime", new Date());
        mongoTemplate.findAndModify(
                query,
                update,
                Chat.class
        );
    }
}
