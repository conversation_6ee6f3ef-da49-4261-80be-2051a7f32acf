package com.sinitek.mind.core.app.service.impl;

import com.sinitek.mind.common.constant.MindConstant;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.entity.AppVersion;
import com.sinitek.mind.core.app.model.*;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.core.app.repository.AppVersionRepository;
import com.sinitek.mind.core.app.service.IAppVersionService;
import com.sinitek.mind.core.app.util.AppUtil;
import com.sinitek.mind.core.app.util.TimeUtils;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppVersionServiceImpl implements IAppVersionService {

    private final AppVersionRepository appVersionRepository;
    private final AppRepository appRepository;
    private final IOperationLogService operationLogService;

    private final MongoTemplate mongoTemplate;

    private final IAccountService accountService;

    @Override
    public AppVersionDTO getAppVersionById(String appId, String versionId, App app) {
        if (versionId != null) {
            AppVersion version = appVersionRepository.findByIdAndAppId(versionId, appId);
            if (version != null) {
                return new AppVersionDTO(
                        version.getId(),
                        version.getVersionName(),
                        version.getNodes(),
                        version.getEdges(),
                        version.getChatConfig() != null ? version.getChatConfig() : (app != null ? app.getChatConfig() : new AppChatConfigType())
                );
            }
        }
        return getLatestAppVersion(appId, app);
    }

    @Override
    public AppVersionDTO getLatestAppVersion(String appId, App app) {
        AppVersion appVersion = appVersionRepository.findFirstByAppIdAndIsPublishOrderByCreateTimeDesc(appId, true);
        if (appVersion != null) {
            return new AppVersionDTO(
                    appVersion.getId(),
                    appVersion.getVersionName(),
                    appVersion.getNodes(),
                    appVersion.getEdges(),
                    appVersion.getChatConfig() != null ?
                            appVersion.getChatConfig() : (app != null ? app.getChatConfig() : new AppChatConfigType())
            );
        }
        return new AppVersionDTO(
                app != null ? app.getPluginData().getNodeVersion() : null,
                app != null ? app.getName() : null,
                app != null ? app.getModules() : Collections.emptyList(),
                app != null ? app.getEdges() : Collections.emptyList(),
                app != null ? app.getChatConfig() : new AppChatConfigType()
        );
    }

    @Override
    public boolean checkIsLatestVersion(String appId, String versionId) {
        if (!ObjectId.isValid(versionId)) {
            return false;
        }
        ObjectId objectId = new ObjectId(versionId);
        AppVersion version = appVersionRepository
                .findFirstByAppIdAndIsPublishAndIdGreaterThan(appId, true, objectId);
        return version == null;
    }

    @Override
    public PageResult<VersionListItemType> getVersionList(VersionListRequest request) {
        // 创建分页对象
        Pageable pageable = PageRequest.of(
                request.getOffset() / request.getPageSize(),
                request.getPageSize(),
                Sort.by(Sort.Direction.DESC, "time")
        );
        // 执行分页查询和总数统计
        Page<AppVersion> versionsPage;
        long total;

        if (request.getIsPublish() != null) {
            // 根据发布状态查询
            versionsPage = appVersionRepository.findByAppIdAndIsPublish(new ObjectId(request.getAppId()), request.getIsPublish(), pageable);
            total = appVersionRepository.countByAppIdAndIsPublish(new ObjectId(request.getAppId()), request.getIsPublish());
        } else {
            // 查询所有版本
            versionsPage = appVersionRepository.findByAppId(new ObjectId(request.getAppId()), pageable);
            total = appVersionRepository.countByAppId(new ObjectId(request.getAppId()));
        }
        
        // 转换结果
        List<VersionListItemType> items = versionsPage.getContent().stream()
                .map(this::converToVersionListItem)
                .toList();
        PageResult<VersionListItemType> result = new PageResult<>();
        result.setTotal((int) total);
        result.setList(items);
        // TODO getSourceMemberInfo
        return result;
    }

    @Override
    public VersionDetailResponse getVersionDetail(VersionDetailRequest request, AuthAppDTO authAppDTO) {
        // 根据版本ID查询版本信息
        Optional<AppVersion> versionOpt = appVersionRepository.findById(request.getVersionId());

        if (versionOpt.isEmpty()) {
            throw new BussinessException("version not found");
        }

        AppVersion version = versionOpt.get();

        // 验证版本是否属于指定的应用
        if (!request.getAppId().equals(version.getAppId())) {
            throw new BussinessException("version does not belong to the specified app");
        }

        // 转换为响应DTO
        VersionDetailResponse response = convertToVersionDetailResponse(version);

        // 重写工作流详情（对应原始的rewriteAppWorkflowToDetail函数）
        if (response.getNodes() != null && !response.getNodes().isEmpty()) {
            RewriteParam rewriteParam = new RewriteParam();
            rewriteParam.setNodes(response.getNodes());
            AppUtil.rewriteAppWorkflowToDetail(
                    response.getNodes(),
                    authAppDTO.getTeamId(),
                    authAppDTO.getIsRoot(),
                    authAppDTO.getTmbId()
            );
        }

        return response;
    }

    @Override
    public LatestVersionResDTO getAppLatestVersion(String appId, AuthAppDTO authAppDTO) {
        try {
            Sort sort = Sort.by(Sort.Direction.DESC, "time");
            List<AppVersion> publishedVersions = appVersionRepository.findLatestPublishByAppId(new ObjectId(appId), sort);
            LatestVersionResDTO resDTO = new LatestVersionResDTO();
            if (!publishedVersions.isEmpty()) {
                AppVersion latestVersion = publishedVersions.get(0); // 获取最新的一个
                List<StoreNodeItemType> nodes = AppUtil.rewriteAppWorkflowToDetail(latestVersion.getNodes(), authAppDTO.getTeamId(), authAppDTO.getIsRoot(), authAppDTO.getTmbId());
                resDTO.setNodes(nodes);
                resDTO.setEdges(latestVersion.getEdges());
                resDTO.setChatConfig(latestVersion.getChatConfig());
                return  resDTO;
            } else {
                Optional<App> appOpt = appRepository.findById(appId);
                if (appOpt.isPresent()) {
                    App app = appOpt.get();
                    resDTO.setNodes(app.getModules());
                    resDTO.setEdges(app.getEdges());
                    resDTO.setChatConfig(app.getChatConfig());
                    return  resDTO;
                } else {
                    throw new BussinessException("app not found");
                }
            }
        } catch (Exception e) {
            throw new BussinessException("获取应用最新版本失败");
        }
    }

    @Override
    public void updateAppVersion(String versionId, String versionName) {
        // 查找版本是否存在
        Optional<AppVersion> versionOpt = appVersionRepository.findById(versionId);
        if (versionOpt.isEmpty()) {
            throw new BussinessException("版本不存在");
        }

        AppVersion version = versionOpt.get();
        version.setVersionName(versionName);

        // 保存更新
        appVersionRepository.save(version);
    }



    @Override
    public PublishAppResponseDTO publishApp(String appId, PublishAppDTO request, AuthAppDTO authAppDTO) {
        try {
            // 1. 更新应用的模块配置（对应原始的MongoApp.findByIdAndUpdate）
            App app = authAppDTO.getApp();

            // 处理自动保存配置（对应原始的autoSave逻辑）
            if (request.getAutoSave() != null &&  request.getAutoSave()) {
                // 这里可以根据需要设置自动保存相关的配置

                // 更新应用的基本配置
                app.setModules(request.getNodes());
                app.setEdges(request.getEdges());
                app.setChatConfig(request.getChatConfig());
                app.setUpdateTime(new Date());
                // 保存应用更新
                appRepository.save(app);
                Map<String,String>  logParams = new HashMap<>();
                logParams.put("appId",appId);
                logParams.put("appName",app.getName());
                logParams.put("appType",app.getType());
                logParams.put("operationName", "account_team:update");
                operationLogService.addOperationLog(MindConstant.UPDATE_PUBLISH_APP, logParams);

                return new PublishAppResponseDTO();
            }

            if (request.getIsPublish()) {
                // 将其他版本的isPublish字段修改为false
                appVersionRepository.updateIsPublishToFalse(appId);
            }

            // 2. 如果需要发布，创建应用版本历史（对应原始的MongoAppVersion.create）
            AppVersion appVersion = new AppVersion();
            appVersion.setAppId(appId);
            appVersion.setVersionName(request.getVersionName());
            appVersion.setNodes(request.getNodes());
            appVersion.setEdges(request.getEdges());
            appVersion.setChatConfig(request.getChatConfig());
            appVersion.setIsPublish(request.getIsPublish());
            appVersion.setTmbId(authAppDTO.getTmbId());
            Date now = new Date();
            appVersion.setCreateTime(now);
            appVersion.setTime(now);
            appVersion.setUpdateTime(now);

            // 保存版本
            AppVersion savedVersion = appVersionRepository.save(appVersion);
            String versionId = savedVersion.getId();

            // update app
            Query query = new Query(Criteria.where("_id").is(new ObjectId(appId)));
            Update update = new Update()
                    .set("modules", request.getNodes())
                    .set("edges", request.getEdges())
                    .set("chatConfig", request.getChatConfig())
                    .set("updateTime", new Date())
                    .set("version", "v2")
                    .set("pluginData.nodeVersion", versionId);

            // 只有发布才会更新定时器
            if (request.getIsPublish() != null && request.getIsPublish()) {
                if (request.getChatConfig() != null) {
                    AppScheduledTriggerConfigType scheduledTriggerConfig = request.getChatConfig().getScheduledTriggerConfig();
                    if (scheduledTriggerConfig != null && scheduledTriggerConfig.getCronString() != null) {
                        update.set("scheduledTriggerConfig", scheduledTriggerConfig);
                        update.set("scheduledTriggerNextTime", TimeUtils.getNextTimeByCronStringAndTimezone(scheduledTriggerConfig.getCronString(), scheduledTriggerConfig.getTimezone()));
                    } else {
                        update.unset("scheduledTriggerConfig");
                        update.unset("scheduledTriggerNextTime");
                    }
                }
            }

            mongoTemplate.updateFirst(query, update, App.class);


            // 3. 构造返回结果
            PublishAppResponseDTO response = new PublishAppResponseDTO();
            response.setSuccess(true);
            response.setVersionId(versionId);

            return response;

        } catch (Exception e) {
            log.error("发布应用失败: {}", e.getMessage(), e);
            throw new BussinessException("发布应用失败: " + e.getMessage());
        }
    }

    /**
     * 将AppVersion实体转换为VersionDetailResponse
     * 对应原始代码中的返回值构造逻辑
     */
    private VersionDetailResponse convertToVersionDetailResponse(AppVersion version) {
        VersionDetailResponse response = new VersionDetailResponse();

        response.set_id(version.getId());
        response.setAppId(version.getAppId());
        response.setTime(version.getCreateTime());
        response.setIsPublish(version.getIsPublish());
        response.setTmbId(version.getTmbId());
        response.setTime(version.getTime());

        // 设置版本名称，如果为空则使用格式化的时间
        if (StringUtils.hasText(version.getVersionName())) {
            response.setVersionName(version.getVersionName());
        } else {
            response.setVersionName(TimeUtils.formatTime2YMDHM(version.getCreateTime()));
        }

        // 从版本配置中提取nodes、edges和chatConfig
        if (version.getNodes() != null) {
            response.setNodes(version.getNodes());
        }
        if (version.getEdges() != null) {

            response.setEdges(version.getEdges());
        }
        if (version.getChatConfig() != null) {
            response.setChatConfig(version.getChatConfig());
        }

        return response;
    }

    private VersionListItemType converToVersionListItem(AppVersion version) {
        VersionListItemType item = new VersionListItemType();
        item.setId(version.getId());
        item.setAppId(version.getAppId());
        item.setVersionName(version.getVersionName());
        item.setTime(version.getTime());
        item.setIsPublish(version.getIsPublish());
        item.setTmbId(version.getTmbId());

        SourceMemberDTO member = accountService.getSourceMemberByOrgId(version.getTmbId());
        item.setSourceMember(member);
        return item;
    }

}
