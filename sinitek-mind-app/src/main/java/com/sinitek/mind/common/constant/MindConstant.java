package com.sinitek.mind.common.constant;

import com.sinitek.sirm.common.log.enumrate.BusinLogType;

import java.util.Arrays;

public class MindConstant {

    // moduleName: 模块名称，必填
    // operateType: 操作类型，必填
    // timingList: 触发时机，非必填，默认值为空，如操作类型是产品管理时我们不知道产品管理具体那些操作会记录业务日志，当传入该值后将展示该值的内容具体看下图效果
    // onlyTenant: 是否近多租户环境下展示，非必填，默认值为false，如机构管理、机构权限管理等只在多租户环境下展示
    // order: 排序值，非必填，默认值为0，值越大展示越在前面


    public static final BusinLogType CREATE_INVITATION_LINK = new BusinLogType("智能体", 1002 , Arrays.asList("创建链接"), false, 0);
    public static final BusinLogType CREATE_APP = new BusinLogType("智能体", 1014 , Arrays.asList("创建应用"), false, 0);
    public static final BusinLogType UPDATE_APP_INFO = new BusinLogType("智能体", 1015 , Arrays.asList("更新应用"), false, 0);
    public static final BusinLogType MOVE_APP = new BusinLogType("智能体", 1016 , Arrays.asList("移动应用"), false, 0);
    public static final BusinLogType DELETE_APP = new BusinLogType("智能体", 1017 , Arrays.asList("删除应用"), false, 0);
    public static final BusinLogType CREATE_APP_COPY = new BusinLogType("智能体", 1021 , Arrays.asList("复制应用"), false, 0);
    public static final BusinLogType CREATE_APP_FOLDER = new BusinLogType("智能体", 1022 , Arrays.asList("创建应用文件夹"), false, 0);
    public static final BusinLogType UPDATE_PUBLISH_APP = new BusinLogType("智能体", 1023 , Arrays.asList("发布应用版本"), false, 0);
    public static final BusinLogType EXPORT_APP_CHAT_LOG = new BusinLogType("智能体", 1027 , Arrays.asList("获取聊天日志列表"), false, 0);
    public static final BusinLogType CREATE_API_KEY = new BusinLogType("智能体", 1052 , Arrays.asList("创建API Key"), false, 0);
    public static final BusinLogType UPDATE_API_KEY = new BusinLogType("智能体", 1053 , Arrays.asList("更新API Key"), false, 0);
    public static final BusinLogType DELETE_API_KEY = new BusinLogType("智能体", 1054 , Arrays.asList("删除API Key"), false, 0);

//    // Team
//    LOGIN("LOGIN", 1001),
//    JOIN_TEAM("JOIN_TEAM", 1003),
//    CHANGE_MEMBER_NAME("CHANGE_MEMBER_NAME", 1004),
//    KICK_OUT_TEAM("KICK_OUT_TEAM", 1005),
//    RECOVER_TEAM_MEMBER("RECOVER_TEAM_MEMBER", 1006),
//    CREATE_DEPARTMENT("CREATE_DEPARTMENT", 1007),
//    CHANGE_DEPARTMENT("CHANGE_DEPARTMENT", 1008),
//    DELETE_DEPARTMENT("DELETE_DEPARTMENT", 1009),
//    RELOCATE_DEPARTMENT("RELOCATE_DEPARTMENT", 1010),
//    CREATE_GROUP("CREATE_GROUP", 1011),
//    DELETE_GROUP("DELETE_GROUP", 1012),
//    ASSIGN_PERMISSION("ASSIGN_PERMISSION", 1013),
//    // APP
//
//    UPDATE_APP_COLLABORATOR("UPDATE_APP_COLLABORATOR", 1018),
//    DELETE_APP_COLLABORATOR("DELETE_APP_COLLABORATOR", 1019),
//    TRANSFER_APP_OWNERSHIP("TRANSFER_APP_OWNERSHIP", 1020),
//    CREATE_APP_PUBLISH_CHANNEL("CREATE_APP_PUBLISH_CHANNEL", 1024),
//    UPDATE_APP_PUBLISH_CHANNEL("UPDATE_APP_PUBLISH_CHANNEL", 1025),
//    DELETE_APP_PUBLISH_CHANNEL("DELETE_APP_PUBLISH_CHANNEL", 1026),
//    // Dataset
//    CREATE_DATASET("CREATE_DATASET", 1028),
//    UPDATE_DATASET("UPDATE_DATASET", 1029),
//    DELETE_DATASET("DELETE_DATASET", 1030),
//    MOVE_DATASET("MOVE_DATASET", 1031),
//    UPDATE_DATASET_COLLABORATOR("UPDATE_DATASET_COLLABORATOR", 1032),
//    DELETE_DATASET_COLLABORATOR("DELETE_DATASET_COLLABORATOR", 1033),
//    TRANSFER_DATASET_OWNERSHIP("TRANSFER_DATASET_OWNERSHIP", 1034),
//    EXPORT_DATASET("EXPORT_DATASET", 1035),
//    CREATE_DATASET_FOLDER("CREATE_DATASET_FOLDER", 1036),
//    // Collection
//    CREATE_COLLECTION("CREATE_COLLECTION", 1037),
//    UPDATE_COLLECTION("UPDATE_COLLECTION", 1038),
//    DELETE_COLLECTION("DELETE_COLLECTION", 1039),
//    RETRAIN_COLLECTION("RETRAIN_COLLECTION", 1040),
//    // Data
//    CREATE_DATA("CREATE_DATA", 1041),
//    UPDATE_DATA("UPDATE_DATA", 1042),
//    DELETE_DATA("DELETE_DATA", 1043),
//    // SearchTest
//    SEARCH_TEST("SEARCH_TEST", 1044),
//    // Account
//    CHANGE_PASSWORD("CHANGE_PASSWORD", 1045),
//    CHANGE_NOTIFICATION_SETTINGS("CHANGE_NOTIFICATION_SETTINGS", 1046),
//    CHANGE_MEMBER_NAME_ACCOUNT("CHANGE_MEMBER_NAME_ACCOUNT", 1047),
//    PURCHASE_PLAN("PURCHASE_PLAN", 1048),
//    EXPORT_BILL_RECORDS("EXPORT_BILL_RECORDS", 1049),
//    CREATE_INVOICE("CREATE_INVOICE", 1050),
//    SET_INVOICE_HEADER("SET_INVOICE_HEADER", 1051),


}
