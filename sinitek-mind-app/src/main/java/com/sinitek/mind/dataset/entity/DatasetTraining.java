package com.sinitek.mind.dataset.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.util.Date;

/**
 * dataset_trainings 实体类
 *
 * <AUTHOR>
 * date 2025-07-22
 * 描述：知识库训练数据表实体
 */
@Data
@Document(collection = "dataset_trainings")
public class DatasetTraining {

    @Id
    private String id;

    @Schema(description = "团队ID")
    private String teamId;

    @Field(targetType = FieldType.OBJECT_ID)
    @Schema(description = "知识库ID")
    private String datasetId;

    @Field(targetType = FieldType.OBJECT_ID)
    @Schema(description = "集合ID")
    private String collectionId;

    @Field(targetType = FieldType.OBJECT_ID)
    @Schema(description = "数据ID")
    private String dataId;

    private String text;
    
    private Integer chunkIndex;
    
    @Schema(description = "状态，见DatasetTrainingStatusEnum，pending=待处理，processed=已处理，failed=处理失败")
    private String status; // pending, processed, failed
    
    private Date createTime;
    
    private Date updateTime;

    @Schema(description = "错误消息")
    private String errorMsg;

    @Schema(description = "重试次数")
    private Integer retryCount;

    @Schema(description = "重建的标识")
    private Boolean rebuilding;
}