package com.sinitek.mind.dataset.service.impl;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.common.util.MindStringUtil;
import com.sinitek.mind.common.util.MongoDBUtil;
import com.sinitek.mind.common.util.TikTokenUtil;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.entity.Dataset;
import com.sinitek.mind.dataset.entity.DatasetCollection;
import com.sinitek.mind.dataset.entity.DatasetData;
import com.sinitek.mind.dataset.repository.DatasetCollectionRepository;
import com.sinitek.mind.dataset.repository.DatasetDataRepository;
import com.sinitek.mind.dataset.repository.DatasetRepository;
import com.sinitek.mind.dataset.service.IDatasetDataService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * dataset_datas 服务实现类
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库数据表Service实现
 */
@Service
public class DatasetDataServiceImpl implements IDatasetDataService {

    @Autowired
    private DatasetDataRepository repository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private DatasetCollectionRepository collectionRepository;

    @Autowired
    private DatasetRepository datasetRepository;

    @Override
    public String create(DatasetDataDTO dto) {
        DatasetData entity = new DatasetData();
        BeanUtils.copyProperties(dto, entity);
        if (CollectionUtils.isNotEmpty(dto.getIndexes())) {
            List<DatasetData.Index> indexList = new ArrayList<>();
            for (DatasetDataDTO.IndexDTO indexDTO : dto.getIndexes()) {
                DatasetData.Index index = new DatasetData.Index();
                BeanUtils.copyProperties(indexDTO, index);
                indexList.add(index);
            }
            entity.setIndexes(indexList);
        }
        entity.setId(null);
        entity.setUpdateTime(new Date());
        DatasetData saved = repository.save(entity);
        return saved.getId();
    }

    @Override
    public void update(DatasetDataDTO dto) {
        if (ObjectUtils.isEmpty(dto) || ObjectUtils.isEmpty(dto.getId())) {
            return;
        }
        DatasetData entity = repository.findById(dto.getId()).orElse(null);
        if (entity == null) {
            return;
        }
        BeanUtils.copyProperties(dto, entity);
        if (CollectionUtils.isNotEmpty(dto.getIndexes())) {
            List<DatasetData.Index> indexList = new ArrayList<>();
            for (DatasetDataDTO.IndexDTO indexDTO : dto.getIndexes()) {
                DatasetData.Index index = new DatasetData.Index();
                BeanUtils.copyProperties(indexDTO, index);
                indexList.add(index);
            }
            entity.setIndexes(indexList);
        }
        repository.save(entity);
    }

    @Override
    public void delete(String id) {
        if (ObjectUtils.isEmpty(id)) {
            return;
        }
        repository.deleteById(id);
    }

    @Override
    public DatasetDataDTO detail(String id) {
        if (ObjectUtils.isEmpty(id)) {
            return null;
        }
        DatasetData entity = repository.findById(id).orElse(null);
        if (entity == null) {
            return null;
        }
        return toDTO(entity);
    }

    @Override
    public PageResult<DatasetDataPageItemDTO> pageList(DatasetDataPageParamDTO param) {
        PageResult<DatasetDataPageItemDTO> result = new PageResult<>();
        Query query = new Query();
        String collectionId = param.getCollectionId();
        if (StringUtils.isNotBlank(collectionId)) {
            query.addCriteria(Criteria.where("collectionId").is(MongoDBUtil.toObjectId(collectionId)));
        }
        String searchText = param.getSearchText();
        if (StringUtils.isNotBlank(searchText)) {
            query.addCriteria(new Criteria().orOperator(
                    Criteria.where("q").regex(searchText),
                    Criteria.where("a").regex(searchText)
            ));
        }
        long total = mongoTemplate.count(query, DatasetData.class);
        result.setTotal((int) total);
        query.with(Sort.by(Sort.Direction.ASC, "chunkIndex")
                .and(Sort.by(Sort.Direction.DESC, "updateTime") ));
        query.skip(param.getOffset() != null ? param.getOffset() : 0);
        query.limit(param.getPageSize() != null ? param.getPageSize() : 20);
        List<DatasetData> entityList = mongoTemplate.find(query, DatasetData.class);
        List<DatasetDataPageItemDTO> dtoList = new ArrayList<>();
        for (DatasetData entity : entityList) {
            DatasetDataPageItemDTO dto = new DatasetDataPageItemDTO();
            dto.set_id(entity.getId());
            dto.setTeamId(entity.getTeamId());
            dto.setDatasetId(entity.getDatasetId());
            dto.setCollectionId(entity.getCollectionId());
            dto.setQ(entity.getQ());
            dto.setA(entity.getA());
            dto.setChunkIndex(entity.getChunkIndex());
            dtoList.add(dto);
        }
        result.setList(dtoList);
        return result;
    }

    @Override
    public String insertData(InsertOneDatasetDataDTO param, String teamId, String tmbId) {
        String collectionId = param.getCollectionId();
        String q = param.getQ();
        String a = param.getA();
        List<InsertOneDatasetDataDTO.IndexDTO> indexes = param.getIndexes();

        if (StringUtils.isBlank(q) || StringUtils.isBlank(collectionId)) {
            throw new IllegalArgumentException("缺少必要参数");
        }

        // 获取集合和数据集信息
        Optional<DatasetCollection> collectionOpt = collectionRepository.findById(collectionId);
        if (!collectionOpt.isPresent()) {
            throw new IllegalArgumentException("集合不存在");
        }
        DatasetCollection collection = collectionOpt.get();
        String datasetId = collection.getDatasetId();
        Optional<Dataset> datasetOpt = datasetRepository.findById(datasetId);
        if (!datasetOpt.isPresent()) {
            throw new IllegalArgumentException("数据集不存在");
        }
        Dataset dataset = datasetOpt.get();
        String vectorModel = dataset.getVectorModel();
        String agentModel = dataset.getAgentModel();
        boolean indexPrefixTitle = true; // 假设或从集合获取
        String name = collection.getName();

        // 格式化文本
        String formatQ = MindStringUtil.simpleText(q);
        String formatA = MindStringUtil.simpleText(a);
        List<DatasetData.Index> formatIndexes = indexes != null ? indexes.stream().map(idx -> {
            DatasetData.Index index = new DatasetData.Index();
            index.setText(MindStringUtil.simpleText(idx.getText()));
            index.setType(idx.getType());
            return index;
        }).collect(Collectors.toList()) : null;

        // 计算令牌
        int token = TikTokenUtil.countPromptTokens(formatQ + formatA, "");

        // TODO: 获取模型数据
        // EmbeddingModelData vectorModelData = getEmbeddingModel(vectorModel);
        // LLMModelData llmModelData = getLLMModel(agentModel);
        // int maxChunkSize = getLLMMaxChunkSize(llmModelData);

        // if (token > maxChunkSize) {
        //     throw new IllegalArgumentException("Content over max chunk size: " + maxChunkSize);
        // }

        // 检查重复
        // TODO: 实现 hasSameValue
        // hasSameValue(teamId, datasetId, collectionId, formatQ, formatA);

        // 创建实体
        DatasetData entity = new DatasetData();
        entity.setTeamId(teamId);
        entity.setTmbId(tmbId);
        entity.setDatasetId(datasetId);
        entity.setCollectionId(collectionId);
        entity.setQ(formatQ);
        entity.setA(formatA);
        entity.setIndexes(formatIndexes);
        entity.setChunkIndex(0);
        entity.setUpdateTime(new Date());

        // TODO: 设置 indexPrefix if needed

        DatasetData saved = repository.save(entity);

        // TODO: 插入后生成向量等
        // int tokens = ...; // from insert
        // pushGenerateVectorUsage(teamId, tmbId, tokens, vectorModel);

        // TODO: 添加审计日志
        // addAuditLog(tmbId, teamId, AuditEventEnum.CREATE_DATA, params);

        return saved.getId();
    }

    @Override
    public void updateDatasetData(DatasetDataUpdateRequest request, String userId, String teamId, String tmbId) throws Exception {
        if (StringUtils.isBlank(request.getDataId())) {
            throw new IllegalArgumentException("数据ID不能为空");
        }

        Optional<DatasetData> optionalEntity = repository.findById(request.getDataId());
        if (optionalEntity.isEmpty()) {
            throw new IllegalArgumentException("数据不存在");
        }

        DatasetData entity = optionalEntity.get();

        // 权限检查 (示例: 检查团队ID)
        if (!entity.getTeamId().equals(teamId)) { // 假设DatasetData有teamId字段
            throw new IllegalArgumentException("无权更新该数据");
        }

        // 更新字段
        if (StringUtils.isNotBlank(request.getQ())) {
            entity.setQ(request.getQ());
        }
        if (request.getA() != null) {
            entity.setA(request.getA());
        }
        if (CollectionUtils.isNotEmpty(request.getIndexes())) {
            List<DatasetData.Index> indexList = new ArrayList<>();
            for (DatasetDataIndexDTO indexDTO : request.getIndexes()) {
                DatasetData.Index index = new DatasetData.Index();
                BeanUtils.copyProperties(indexDTO, index);
                indexList.add(index);
            }
            entity.setIndexes(indexList);
        } else {
            entity.setIndexes(new ArrayList<>());
        }

        // TODO: 计算tokens如果需要
        // String fullText = entity.getQ() + entity.getA();
        // int tokens = TikTokenUtil.tokens(fullText);
        // entity.setTokens(tokens);

        entity.setUpdateTime(new Date());

        repository.save(entity);
    }

    @Override
    public int countByCollectionId(String collectionId) {
        if (StringUtils.isBlank(collectionId)) {
            return 0;
        }
        Query query = new Query(Criteria.where("collectionId").is(collectionId));
        return (int) mongoTemplate.count(query, DatasetData.class);
    }

    @Override
    public void deleteByCollectionId(String collectionId) {
        if (StringUtils.isBlank(collectionId)) {
            return;
        }
        Query query = new Query(Criteria.where("collectionId").is(collectionId));
        mongoTemplate.remove(query, DatasetData.class);
    }

    private DatasetDataDTO toDTO(DatasetData entity) {
        DatasetDataDTO dto = new DatasetDataDTO();
        BeanUtils.copyProperties(entity, dto);
        if (CollectionUtils.isNotEmpty(entity.getIndexes())) {
            List<DatasetDataDTO.IndexDTO> indexDTOList = new ArrayList<>();
            for (DatasetData.Index index : entity.getIndexes()) {
                DatasetDataDTO.IndexDTO indexDTO = new DatasetDataDTO.IndexDTO();
                BeanUtils.copyProperties(index, indexDTO);
                indexDTO.set_id(index.getId());  // Assuming entity has getId(), adjust if necessary
                indexDTOList.add(indexDTO);
            }
            dto.setIndexes(indexDTOList);
        } else {
            dto.setIndexes(new ArrayList<>());
        }
        return dto;
    }

    private List<DatasetDataDTO> toDTOList(List<DatasetData> list) {
        List<DatasetDataDTO> dtoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (DatasetData entity : list) {
                dtoList.add(toDTO(entity));
            }
        }
        return dtoList;
    }
}