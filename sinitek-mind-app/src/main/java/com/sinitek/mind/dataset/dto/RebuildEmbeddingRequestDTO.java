package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "重建嵌入向量请求DTO")
public class RebuildEmbeddingRequestDTO {

    @NotBlank(message = "datasetId不能为空")
    @Schema(description = "数据集ID")
    private String datasetId;

    @NotBlank(message = "vectorModel不能为空")
    @Schema(description = "向量模型")
    private String vectorModel;
}