package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.support.common.dto.BaseFileDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 知识库集合详情DTO
 *
 * <AUTHOR>
 * date 2025-07-16
 */
@Data
@Schema(description = "知识库集合详情DTO")
public class DatasetCollectionDetailDTO {

    @Schema(description = "ID")
    private String _id;

    @Schema(description = "父ID")
    private String parentId;

    @Schema(description = "团队ID")
    private String teamId;

    @Schema(description = "TMB ID")
    private String tmbId;

    @Schema(description = "知识库ID")
    private String datasetId;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "版本")
    private int __v;

    @Schema(description = "知识库详情")
    private DatasetDTO dataset;

    @Schema(description = "索引数量")
    private int indexAmount;

    @Schema(description = "来源名称")
    private String sourceName;

    @Schema(description = "权限")
    private DatasetCollectionListV2PermissionDTO permission;

    @Schema(description = "错误数量")
    private int errorCount;

    @Schema(description = "文件ID（type=file时有值）")
    private String fileId;

    @Schema(description = "集合原文件信息")
    private BaseFileDTO file;

    @Schema(description = "自定义PDF解析")
    private Boolean customPdfParse;

    @Schema(description = "训练类型")
    private String trainingType;

    @Schema(description = "分块触发类型")
    private String chunkTriggerType;

    @Schema(description = "分块触发最小值")
    private Integer chunkTriggerMinSize;

    @Schema(description = "数据增强集合名")
    private Boolean dataEnhanceCollectionName;

    @Schema(description = "图片索引")
    private Boolean imageIndex;

    @Schema(description = "自动索引")
    private Boolean autoIndexes;

    @Schema(description = "分块设置模式")
    private String chunkSettingMode;

    @Schema(description = "分块拆分模式")
    private String chunkSplitMode;

    @Schema(description = "段落分块AI模式")
    private String paragraphChunkAIMode;

    @Schema(description = "段落分块深度")
    private Integer paragraphChunkDeep;

    @Schema(description = "段落分块最小值")
    private Integer paragraphChunkMinSize;

    @Schema(description = "分块大小")
    private Integer chunkSize;

    @Schema(description = "索引大小")
    private Integer indexSize;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "hash原文")
    private String hashRawText;

    @Schema(description = "原文长度")
    private Integer rawTextLength;

    @Schema(description = "是否禁用")
    private Boolean forbid;

    @Schema(description = "版本号")
    private Integer version;
}