package com.sinitek.mind.model.core.rerank;

import com.sinitek.mind.model.core.rerank.provider.RerankModelProvider;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import dev.langchain4j.model.scoring.ScoringModel;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 重排模型工厂层
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Component
public class RerankModelFactory {

    @Autowired
    private ISystemModelService systemModelService;

    @Autowired
    private List<RerankModelProvider> rerankModelProviders;

    /**
     * 创建重排模型
     * @param modelId 模型ID
     * @return 重排模型
     */
    public ScoringModel getRerankModel(String modelId) {
        SystemModelDTO modelDTO = getModelDTO(modelId);
        return createRerankModel(modelDTO);
    }

    /**
     * 根据模型ID获取模型详情并创建重排模型
     * @param modelId 模型ID
     * @return 包含重排模型和模型详情的对象
     */
    public ModelWithConfig getModelWithConfig(String modelId) {
        SystemModelDTO modelDTO = getModelDTO(modelId);
        ScoringModel rerankModel = createRerankModel(modelDTO);
        return new ModelWithConfig(rerankModel, modelDTO);
    }

    /**
     * 根据模型配置创建重排模型
     * @param modelDTO 模型配置
     * @return 重排模型实例
     */
    public ScoringModel createRerankModel(SystemModelDTO modelDTO) {
        if (ObjectUtils.isEmpty(modelDTO)) {
            throw new IllegalArgumentException("模型配置不能为空");
        }

        String provider = modelDTO.getProvider();
        if (StringUtils.isBlank(provider)) {
            throw new IllegalArgumentException("模型提供商不能为空");
        }

        // 查找对应的提供商实现
        for (RerankModelProvider rerankModelProvider : rerankModelProviders) {
            if (rerankModelProvider.supports(provider)) {
                return rerankModelProvider.createRerankModel(modelDTO);
            }
        }

        throw new IllegalArgumentException("不支持的重排模型提供商: " + provider);
    }

    /**
     * 获取模型配置信息
     * @param modelId 模型ID
     * @return 模型配置信息
     */
    private SystemModelDTO getModelDTO(String modelId) {
        // 直接从服务获取模型详情
        SystemModelDTO modelDTO = systemModelService.getModelDetail(modelId);

        if (ObjectUtils.isEmpty(modelDTO)) {
            throw new IllegalArgumentException("模型不存在: " + modelId);
        }

        return modelDTO;    }
    
    /**
     * 重排模型与模型配置的包装类
     */
    public static class ModelWithConfig {

        private final ScoringModel rerankModel;
        private final SystemModelDTO modelDTO;

        public ModelWithConfig(ScoringModel rerankModel, SystemModelDTO modelDTO) {
            this.rerankModel = rerankModel;
            this.modelDTO = modelDTO;
        }

        public ScoringModel getRerankModel() {
            return rerankModel;
        }

        public SystemModelDTO getModelDTO() {
            return modelDTO;
        }
    }
}