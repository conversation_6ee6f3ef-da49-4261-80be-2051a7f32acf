package com.sinitek.mind.model.core.rerank;

import dev.langchain4j.model.scoring.ScoringModel;
import dev.langchain4j.rag.content.Content;
import dev.langchain4j.rag.content.DefaultContent;
import dev.langchain4j.rag.content.aggregator.ContentAggregator;
import dev.langchain4j.rag.content.aggregator.ReRankingContentAggregator;
import dev.langchain4j.rag.query.Query;
import org.springframework.ai.document.Document;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 重排模型支持类
 *
 * <AUTHOR>
 * date 2025-08-06
 */
@Component
public class RerankModelSupport {

    private static final Logger log = LoggerFactory.getLogger(RerankModelSupport.class);

    /**
     * 组装ContentAggregator
     *  - 该类的作用：实现传递问题和向量结果片段，排序过滤出最终结果
     * @param scoringModel
     * @param minScore
     * @return
     */
    public ContentAggregator buildContentAggregator(ScoringModel scoringModel, Double minScore) {
        return ReRankingContentAggregator.builder()
                .scoringModel(scoringModel)
                .minScore(minScore)
                .build();
    }

    /**
     * 对文档进行重排
     * @param query 查询字符串
     * @param documents 文档列表
     * @param scoringModel 评分模型
     * @param minScore 最小分数阈值
     * @return 重排后的内容列表
     */
    public List<Content> rerankDocuments(String query, List<Document> documents, ScoringModel scoringModel, Double minScore) {
        Query queryObj = new Query(query);
        List<Content> contents = new ArrayList<>();
        for (Document document : documents) {
            Content content1 = new DefaultContent(document.getText());
            contents.add(content1);
        }
        Collection<List<Content>> contentCollection = new ArrayList<>();
        contentCollection.add(contents);
        Map<Query, Collection<List<Content>>> queryToContents = new HashMap<>();
        queryToContents.put(queryObj, contentCollection);
        ContentAggregator contentAggregator = buildContentAggregator(scoringModel, minScore);
        return contentAggregator.aggregate(queryToContents);
    }
}
