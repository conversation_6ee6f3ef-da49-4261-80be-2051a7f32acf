# 流程编排消息码规则
# 30-00-0000
# [流程编排固定30-模块-错误码]

# 01 - audit
# 02 - common
# 03 - execution
# 04 - node
# 05 - workflow

30050001=流程编排不存在
30050002=流程编排JSON序列化失败
30050003=参数不能为空
30050004=流程ID无效
30050005=流程数据不能为空
30050006=参数类型转换失败
30050007=meta信息不能为空
30050008=position数据不能为空
30050009=节点信息不能为空
30050010=节点配置JSON序列化失败
30050011=发布失败
30050012=更新失败
30050013=应用历史失败
30050014=生效失败
30050015=节点不存在
30050016=节点类型不存在
30050017=该类型已被节点使用，不可删除
30050018=连接线不存在
30050019=流程编排保存失败
30050020=节点ID不能为空