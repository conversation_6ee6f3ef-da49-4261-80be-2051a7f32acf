# 流程編排消息碼規則
# 30-00-0000
# [流程編排固定30-模塊-錯誤碼]

# 01 - audit
# 02 - common
# 03 - execution
# 04 - node
# 05 - workflow

30050001=流程編排不存在
30050002=流程編排JSON序列化失敗
30050003=參數不能為空
30050004=流程ID無效
30050005=流程數據不能為空
30050006=參數類型轉換失敗
30050007=meta信息不能為空
30050008=position數據不能為空
30050009=節點信息不能為空
30050010=節點配置JSON序列化失敗
30050011=發佈失敗
30050012=更新失敗
30050013=應用歷史失敗
30050014=生效失敗
30050015=節點不存在
30050016=節點類型不存在
30050017=該類型已被節點使用，不可刪除
30050018=連接綫不存在
30050019=流程編排保存失敗
30050020=節點ID不能爲空