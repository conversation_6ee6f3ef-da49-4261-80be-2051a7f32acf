<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sinitek.sinicube</groupId>
        <artifactId>sini-mind-backend</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>sinitek-cloud-mind-sdk</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-mind-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-mind-app</artifactId>
            <exclusions>
              <exclusion>
                <artifactId>sinitek-sirmapp</artifactId>
                <groupId>com.sinitek.sinicube</groupId>
              </exclusion>
              <exclusion>
                <artifactId>postgresql</artifactId>
                <groupId>org.postgresql</groupId>
              </exclusion>
              <exclusion>
                <artifactId>sinitek-mind-plugins-vector-store-milvus</artifactId>
                <groupId>com.sinitek.sinicube</groupId>
              </exclusion>
              <exclusion>
                <artifactId>langchain4j-community-xinference</artifactId>
                <groupId>dev.langchain4j</groupId>
              </exclusion>
              <exclusion>
                <artifactId>spring-ai-advisors-vector-store</artifactId>
                <groupId>org.springframework.ai</groupId>
              </exclusion>
              <exclusion>
                <artifactId>spring-ai-jsoup-document-reader</artifactId>
                <groupId>org.springframework.ai</groupId>
              </exclusion>
              <exclusion>
                <artifactId>spring-ai-markdown-document-reader</artifactId>
                <groupId>org.springframework.ai</groupId>
              </exclusion>
              <exclusion>
                 <artifactId>spring-ai-pdf-document-reader</artifactId>
                 <groupId>org.springframework.ai</groupId>
              </exclusion>
              <exclusion>
                <artifactId>spring-ai-rag</artifactId>
                <groupId>org.springframework.ai</groupId>
              </exclusion>
              <exclusion>
                <artifactId>spring-ai-starter-mcp-client-webflux</artifactId>
                <groupId>org.springframework.ai</groupId>
              </exclusion>
              <exclusion>
                <artifactId>spring-ai-starter-model-ollama</artifactId>
                <groupId>org.springframework.ai</groupId>
              </exclusion>
              <exclusion>
                <artifactId>spring-ai-starter-model-openai</artifactId>
                <groupId>org.springframework.ai</groupId>
              </exclusion>
              <exclusion>
                <artifactId>spring-ai-tika-document-reader</artifactId>
                <groupId>org.springframework.ai</groupId>
              </exclusion>
              <exclusion>
                <artifactId>spring-boot-starter-data-mongodb</artifactId>
                <groupId>org.springframework.boot</groupId>
              </exclusion>
              <exclusion>
                <artifactId>spring-boot-starter-jdbc</artifactId>
                <groupId>org.springframework.boot</groupId>
              </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
    </dependencies>
    
</project>