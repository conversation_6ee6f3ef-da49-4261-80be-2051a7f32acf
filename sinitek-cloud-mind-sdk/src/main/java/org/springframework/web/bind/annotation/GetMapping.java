package org.springframework.web.bind.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * JDK 8 兼容的 GetMapping 注解
 * 这是一个简化版本，仅用于编译时兼容，不提供实际的 Spring Web 功能
 * 
 * <AUTHOR> for JDK 8 compatibility
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface GetMapping {
    
    /**
     * 请求路径
     */
    String[] value() default {};
    
    /**
     * 请求路径（别名）
     */
    String[] path() default {};
    
    /**
     * 请求参数
     */
    String[] params() default {};
    
    /**
     * 请求头
     */
    String[] headers() default {};
    
    /**
     * 消费的媒体类型
     */
    String[] consumes() default {};
    
    /**
     * 产生的媒体类型
     */
    String[] produces() default {};
}
