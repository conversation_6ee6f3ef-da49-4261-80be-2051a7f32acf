package org.springframework.web.bind.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * JDK 8 兼容的 RequestParam 注解
 * 这是一个简化版本，仅用于编译时兼容，不提供实际的 Spring Web 功能
 * 
 * <AUTHOR> for JDK 8 compatibility
 */
@Target(ElementType.PARAMETER)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequestParam {
    
    /**
     * 参数名称
     */
    String value() default "";
    
    /**
     * 参数名称（别名）
     */
    String name() default "";
    
    /**
     * 是否必需
     */
    boolean required() default true;
    
    /**
     * 默认值
     */
    String defaultValue() default "\n\t\t\n\t\t\n\uE000\uE001\uE002\n\t\t\t\t\n";
}
