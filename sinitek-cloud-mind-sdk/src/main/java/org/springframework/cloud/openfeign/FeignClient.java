package org.springframework.cloud.openfeign;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * JDK 8 兼容的 FeignClient 注解
 * 这是一个简化版本，仅用于编译时兼容，不提供实际的 Feign 功能
 * 
 * <AUTHOR> for JDK 8 compatibility
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface FeignClient {
    
    /**
     * 服务名称
     */
    String name() default "";
    
    /**
     * 服务名称（别名）
     */
    String value() default "";
    
    /**
     * 上下文ID
     */
    String contextId() default "";
    
    /**
     * 服务URL
     */
    String url() default "";
    
    /**
     * 是否解码404错误
     */
    boolean decode404() default false;
    
    /**
     * 配置类
     */
    Class<?>[] configuration() default {};
    
    /**
     * 回退类
     */
    Class<?> fallback() default void.class;
    
    /**
     * 回退工厂类
     */
    Class<?> fallbackFactory() default void.class;
    
    /**
     * 路径前缀
     */
    String path() default "";
    
    /**
     * 是否为主要bean
     */
    boolean primary() default true;
}
