package com.sinitek.mind.support.permission.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 协作者信息DTO
 *
 * <AUTHOR> Team
 * @date 2025/1/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "协作者信息对象")
public class CollaboratorDTO {

    @Schema(description = "团队ID")
    private String teamId;

    @Schema(description = "权限结果对象")
    private PermissionDTO permission;

    @Schema(description = "协作者名称")
    private String name;

    @Schema(description = "协作者头像URL")
    private String avatar;

    @Schema(description = "团队成员ID（与groupId、orgId三选一）")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String tmbId;

    @Schema(description = "群组ID（与tmbId、orgId三选一）")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String groupId;

    @Schema(description = "组织ID（与tmbId、groupId三选一）")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orgId;
}