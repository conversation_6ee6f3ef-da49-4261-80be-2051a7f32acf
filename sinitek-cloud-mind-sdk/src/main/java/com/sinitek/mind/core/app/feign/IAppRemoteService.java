package com.sinitek.mind.core.app.feign;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.support.permission.dto.CollaboratorDTO;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2025-08-07 08:45
 */
@FeignClient(
    name = "${sinicube.mind.remote.service-name:CLOUD-MIND}",
    contextId = "mindAppRemoteService",
    url = "${sinicube.mind.remote.url:}"
)
public interface IAppRemoteService {

    @GetMapping("/mind/api/core/app/collaborator/list")
    @Operation(summary = "获取App协作者列表")
    ApiResponse<List<CollaboratorDTO>> getCollaboratorList(@RequestParam String appId);

}
