<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sinitek.sinicube</groupId>
        <artifactId>sinitek-mind-plugins</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>sinitek-mind-plugins-vector-store-milvus</artifactId>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.sinitek.sinicube</groupId>
            <artifactId>sinitek-mind-api</artifactId>
        </dependency>

        <!-- milvus作为向量数据库 -->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-vector-store-milvus</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <!-- JDK8 profile: 排除不支持 JDK 8 的依赖和源文件 -->
        <profile>
            <id>jdk8</id>
            <dependencies>
                <dependency>
                    <groupId>com.sinitek.sinicube</groupId>
                    <artifactId>sinitek-mind-api</artifactId>
                    <version>${project.version}</version>
                    <classifier>jdk8</classifier>
                </dependency>
            </dependencies>
            <build>
                <plugins>
                    <!-- 配置编译器排除使用 Spring AI 的源文件 -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-compiler-plugin</artifactId>
                        <configuration>
                            <source>1.8</source>
                            <target>1.8</target>
                            <encoding>UTF-8</encoding>
                            <excludes>
                                <!-- 排除所有使用 Spring AI 的源文件 -->
                                <exclude>**/*.java</exclude>
                            </excludes>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
